# ActionHistoryTable Component

## Descrição

O componente `ActionHistoryTable` é uma implementação robusta e elegante para exibir o histórico de ações do sistema. Ele utiliza o `EasyDataTable` para fornecer uma interface rica com filtros avançados, paginação, ordenação e funcionalidades de exportação.

## Características

### 🔍 **Filtros Avançados**
- **Filtro por Data**: Data inicial e final para delimitar período
- **Tipo de Ação**: Criação, Atualização, Exclusão, Login, Logout, etc.
- **Entidade**: Paciente, Dentista, Clínica, Consulta, Usuário
- **Usuário**: Busca por nome do usuário que executou a ação
- **Método HTTP**: GET, POST, PUT, PATCH, DELETE
- **Filtros Colapsáveis**: Interface limpa com opção de mostrar/ocultar filtros

### 📊 **Estatísticas em Tempo Real**
- Total de ações registradas
- Ações realizadas hoje
- Usuários ativos
- Entidades afetadas

### 📋 **Tabela Robusta**
- **Paginação Server-side**: Carregamento eficiente de grandes volumes de dados
- **Ordenação**: Por data, tipo de ação, método HTTP
- **Busca em Tempo Real**: Com debounce para otimização
- **Responsiva**: Adaptável a diferentes tamanhos de tela

### 🎨 **Interface Elegante**
- **Design Moderno**: Cards com gradientes e sombras suaves
- **Badges Coloridos**: Identificação visual rápida de tipos de ação e métodos HTTP
- **Hover Effects**: Interações visuais suaves
- **Loading States**: Indicadores de carregamento

### 📤 **Exportação**
- Exportação em CSV com filtros aplicados
- Download automático do arquivo
- Nome do arquivo com data atual

### 🔍 **Detalhes Completos**
- Modal com informações detalhadas de cada registro
- Visualização de dados anteriores e novos (para updates)
- Informações de IP, User Agent, endpoint
- Formatação JSON legível

## Uso

### Implementação Básica
```vue
<template>
  <ActionHistoryTable />
</template>

<script>
import ActionHistoryTable from '@/components/ActionHistoryTable.vue';

export default {
  components: {
    ActionHistoryTable
  }
}
</script>
```

### Service Associado
O componente utiliza o `actionHistoryService.js` que fornece:

- `getActionHistory(filters)` - Busca histórico com filtros
- `getActionHistoryStats()` - Estatísticas do sistema
- `exportActionHistory(filters, format)` - Exportação de dados
- `getActionHistoryRecord(id)` - Detalhes de um registro específico

## Estrutura de Dados

### Registro de Histórico
```javascript
{
  id: 1,
  user_id: 123,
  action_type: 'create',
  action_description: 'Paciente criado com sucesso',
  http_method: 'POST',
  endpoint: '/api/patients',
  patient_id: 456,
  dentist_id: null,
  clinica_id: 789,
  entity_type: 'patient',
  entity_id: 456,
  old_data: null,
  new_data: '{"nome": "João Silva", "email": "<EMAIL>"}',
  ip_address: '*************',
  user_agent: 'Mozilla/5.0...',
  created_at: '2024-01-15T10:30:00Z',
  user: {
    id: 123,
    nome: 'Dr. Maria Santos',
    email: '<EMAIL>'
  },
  patient: {
    id: 456,
    nome: 'João Silva'
  }
}
```

## Filtros Disponíveis

| Filtro | Tipo | Descrição |
|--------|------|-----------|
| `date_from` | Date | Data inicial |
| `date_to` | Date | Data final |
| `action_type` | String | Tipo de ação (create, update, delete, etc.) |
| `entity_type` | String | Tipo de entidade (patient, dentist, clinic, etc.) |
| `user_search` | String | Busca por nome/email do usuário |
| `http_method` | String | Método HTTP (GET, POST, PUT, etc.) |
| `page` | Number | Página atual |
| `per_page` | Number | Registros por página |
| `sort_by` | String | Campo para ordenação |
| `sort_type` | String | Tipo de ordenação (asc, desc) |

## Personalização

### Cores dos Badges
As cores dos badges são definidas nos métodos:
- `getActionTypeClass()` - Cores por tipo de ação
- `getHttpMethodClass()` - Cores por método HTTP

### Labels
Os labels são definidos em:
- `getActionTypeLabel()` - Labels dos tipos de ação
- `getEntityLabel()` - Labels dos tipos de entidade

## Integração com Backend

O componente espera que o backend implemente as seguintes rotas:

```php
Route::group(['prefix' => 'action-history'], function () {
    Route::get('', [ActionHistoryController::class, 'index']);
    Route::get('stats', [ActionHistoryController::class, 'getStats']);
    Route::get('export', [ActionHistoryController::class, 'export']);
    Route::get('{action_history}', [ActionHistoryController::class, 'show']);
});
```

## Performance

- **Paginação Server-side**: Evita carregar todos os registros de uma vez
- **Debounce na Busca**: Reduz requisições desnecessárias
- **Lazy Loading**: Carregamento sob demanda
- **Cache de Estatísticas**: Estatísticas carregadas apenas uma vez

## Acessibilidade

- Labels descritivos em todos os campos
- Títulos informativos nos botões
- Estados de loading claramente indicados
- Navegação por teclado suportada

## Responsividade

- Layout adaptável para mobile e desktop
- Tabela com scroll horizontal em telas pequenas
- Filtros empilhados em dispositivos móveis
- Botões com tamanhos apropriados para touch
