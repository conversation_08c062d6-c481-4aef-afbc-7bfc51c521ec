<template>
  <div class="modelo3d-viewer-wrapper">
    <div class="modelo3d-viewer-container">
      <!-- Loader para o modelo 3D -->
      <div v-if="isLoading" class="stl-loader-container">
        <div class="stl-loader-spinner">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Carregando...</span>
          </div>
        </div>
        <div class="stl-loader-progress">
          <div class="progress">
            <div
              class="progress-bar progress-bar-striped progress-bar-animated"
              role="progressbar"
              :style="{ width: loadingProgress + '%' }"
              :aria-valuenow="loadingProgress"
              aria-valuemin="0"
              aria-valuemax="100"
            >
            </div>
          </div>
          <div class="stl-loader-text">Carregando modelo 3D...</div>
        </div>
      </div>

      <!-- Container para o visualizador 3D -->
      <div
        ref="viewerContainer"
        class="online-3d-viewer"
        :class="{ 'hidden': isLoading, 'fullscreen-mode': isFullscreen }"
      ></div>

      <!-- Overlay de tela cheia -->
      <div class="fullscreen-overlay" v-if="isFullscreen"></div>

      <!-- Botão para sair do modo tela cheia -->
      <button
        v-if="isFullscreen"
        class="exit-fullscreen-btn"
        title="Sair da tela cheia"
        @click="toggleFullscreen"
      >
        <font-awesome-icon :icon="['fas', 'compress-alt']" />
      </button>

      <!-- Controles do visualizador (versão desktop) -->
      <div class="viewer-controls desktop-controls" v-if="!isLoading">
        <!-- Controles principais -->
        <div class="controls-group main-controls">
          <button
            class="control-btn"
            :class="{ 'active': showEdges }"
            title="Mostrar/Ocultar Linhas"
            @click="toggleEdges"
          >
            <i class="fas fa-border-all"></i>
          </button>
          <button
            class="control-btn"
            title="Resetar Visualização"
            @click="resetView"
          >
            <i class="fas fa-home"></i>
          </button>
          <button
            class="control-btn"
            :class="{ 'active': isFullscreen }"
            title="Modo Tela Cheia"
            @click="toggleFullscreen"
          >
            <i :class="isFullscreen ? 'fas fa-compress-alt' : 'fas fa-expand-alt'"></i>
          </button>
        </div>

        <!-- Controles de rotação -->
        <div class="controls-group rotation-controls">
          <button class="control-btn" title="Rotacionar para cima" @click="rotateModel('up')">
            <i class="fas fa-chevron-up"></i>
          </button>
          <div class="horizontal-controls">
            <button class="control-btn" title="Rotacionar para esquerda" @click="rotateModel('left')">
              <i class="fas fa-chevron-left"></i>
            </button>
            <button class="control-btn" title="Rotacionar para direita" @click="rotateModel('right')">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
          <button class="control-btn" title="Rotacionar para baixo" @click="rotateModel('down')">
            <i class="fas fa-chevron-down"></i>
          </button>
        </div>

        <!-- Controles de zoom -->
        <div class="controls-group zoom-controls">
          <button class="control-btn" title="Aumentar zoom" @click="zoomModel('in')">
            <i class="fas fa-plus"></i>
          </button>
          <button class="control-btn" title="Diminuir zoom" @click="zoomModel('out')">
            <i class="fas fa-minus"></i>
          </button>
        </div>

        <!-- Controles de visualização -->
        <div class="controls-group view-controls">
          <button class="control-btn" title="Vista frontal" @click="setModelView('front')">
            <i class="fas fa-eye"></i>
          </button>
          <button class="control-btn" title="Vista lateral" @click="setModelView('side')">
            <i class="fas fa-eye"></i>
          </button>
          <button class="control-btn" title="Vista superior" @click="setModelView('top')">
            <i class="fas fa-eye"></i>
          </button>
        </div>
      </div>

      <!-- Informações do modelo -->
      <div class="modelo-info-card" v-if="!isLoading">
        <div class="info-card-content">
          <div class="info-card-header">
            <font-awesome-icon :icon="['fas', isGroupMode ? 'layer-group' : 'cube']" class="info-icon" />
            <span class="info-title">{{ isGroupMode ? grupoNome : modeloDescricao }}</span>
          </div>
          <div class="info-card-body">
            <div v-if="pacienteNome" class="info-item">
              <font-awesome-icon :icon="['fas', 'user']" class="info-icon-small" />
              <span>{{ pacienteNome }}</span>
            </div>
            <div v-if="isGroupMode && grupoModelos.length" class="info-item">
              <font-awesome-icon :icon="['fas', 'cubes']" class="info-icon-small" />
              <span>{{ grupoModelos.length }} modelo{{ grupoModelos.length !== 1 ? 's' : '' }}</span>
            </div>
            <div v-else-if="modeloData" class="info-item">
              <font-awesome-icon :icon="['fas', 'calendar-alt']" class="info-icon-small" />
              <span>{{ $filters.dateDmy(modeloData) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Instruções de uso -->
      <div class="viewer-instructions" v-if="!isLoading">
        <div class="instructions-content">
          <div><strong>Rotação:</strong> clique esquerdo e arraste</div>
          <div><strong>Zoom:</strong> scroll do mouse</div>
          <div><strong>Mover:</strong> clique direito e arraste</div>
          <div><strong>Resetar:</strong> botão Home ou duplo clique</div>
        </div>
      </div>
    </div>

    <!-- Botão de voltar para mobile -->
    <button
      class="mobile-back-btn"
      v-if="!isLoading"
      title="Voltar"
      @click="closeViewer"
    >
      <i class="fas fa-times" style="font-size: 1.4rem;"></i>
    </button>
  </div>
</template>

<script>
import * as OV from 'online-3d-viewer';
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { downloadModelo3D } from "@/services/modelos3dService";
import cSwal from "@/utils/cSwal.js";

export default {
  name: 'Modelos3DViewer',
  props: {
    // Props para modelo único
    modelUrl: {
      type: String,
      required: false
    },
    pacienteNome: {
      type: String,
      default: ''
    },
    modeloDescricao: {
      type: String,
      default: 'Modelo 3D'
    },
    modeloData: {
      type: String,
      default: ''
    },
    // Props para modo de grupo
    isGroupMode: {
      type: Boolean,
      default: false
    },
    grupoModelos: {
      type: Array,
      default: () => []
    },
    modelosVisiveis: {
      type: Object,
      default: () => ({})
    },
    grupoNome: {
      type: String,
      default: ''
    }
  },
  emits: ['error', 'close'],
  setup(props, { emit }) {
    const viewerContainer = ref(null);
    const isLoading = ref(true);
    const loadingProgress = ref(0);
    const isFullscreen = ref(false);
    const showEdges = ref(false);
    let viewer = null;
    let progressInterval = null;

    // Função para inicializar o visualizador
    const initViewer = async () => {
      // Verificar se o elemento de referência está disponível
      if (!viewerContainer.value) {
        console.error('Elemento de referência não disponível');
        // Tentar novamente após um pequeno delay
        setTimeout(initViewer, 100);
        return;
      }

      isLoading.value = true;
      loadingProgress.value = 0;

      // Simular progresso de download
      progressInterval = setInterval(() => {
        if (loadingProgress.value < 90) {
          loadingProgress.value += Math.floor(Math.random() * 10) + 1;
        }
      }, 200);

      try {
        let modelDataArray = [];

        if (props.isGroupMode && props.grupoModelos.length > 0) {
          // Modo grupo: baixar todos os modelos
          for (const modelo of props.grupoModelos) {
            const modelData = await downloadModelo3D(modelo.url);
            modelDataArray.push({
              data: modelData,
              nome: modelo.descricao || `Modelo ${modelDataArray.length + 1}`,
              id: modelo.id
            });
          }
        } else if (props.modelUrl) {
          // Modo modelo único
          const modelData = await downloadModelo3D(props.modelUrl);
          modelDataArray.push({
            data: modelData,
            nome: props.modeloDescricao,
            id: 'single'
          });
        }

        // Completar o progresso
        clearInterval(progressInterval);
        loadingProgress.value = 100;

        // Pequeno delay para mostrar 100% antes de inicializar o visualizador
        setTimeout(() => {
          // Inicializar o visualizador
          initializeViewer(modelDataArray);

          // Esconder o loader
          isLoading.value = false;
        }, 300);

      } catch (error) {
        clearInterval(progressInterval);
        console.error('Erro ao baixar modelo 3D:', error);
        cSwal.cError("Erro ao carregar o modelo 3D. Por favor, tente novamente.");
        emit('error', error);
      }
    };

    // Função para inicializar o visualizador com os dados dos modelos
    const initializeViewer = async (modelDataArray) => {
      try {
        // Verificar se o elemento de referência está disponível
        if (!viewerContainer.value) {
          console.error('Elemento de referência não disponível');
          // Tentar novamente após um pequeno delay para garantir que o DOM esteja pronto
          setTimeout(() => initializeViewer(modelDataArray), 100);
          return;
        }

        // Garantir que o container tenha dimensões
        if (viewerContainer.value.clientWidth === 0 || viewerContainer.value.clientHeight === 0) {
          viewerContainer.value.style.width = '100%';
          viewerContainer.value.style.height = '500px';
        }

        // Inicializar o visualizador
        viewer = new OV.EmbeddedViewer(viewerContainer.value, {
          backgroundColor: new OV.RGBAColor(170, 175, 195, 255), // Cinza azulado para melhor contraste e elegância
          defaultColor: new OV.RGBColor(255, 255, 255), // Branco por padrão
          edgeSettings: new OV.EdgeSettings(showEdges.value, new OV.RGBColor(0, 0, 0), 1),
          onModelLoaded: () => {
            console.log('Modelos carregados com sucesso');

            // Aplicar cores diferentes para cada modelo se for modo grupo
            if (props.isGroupMode && viewer) {
              const colors = [
                new OV.RGBColor(100, 149, 237), // Azul claro
                new OV.RGBColor(255, 182, 193), // Rosa claro
                new OV.RGBColor(144, 238, 144), // Verde claro
                new OV.RGBColor(255, 218, 185), // Pêssego
                new OV.RGBColor(221, 160, 221), // Ameixa
                new OV.RGBColor(255, 255, 224), // Amarelo claro
              ];

              // Aplicar cores diferentes para cada modelo
              for (let i = 0; i < modelDataArray.length; i++) {
                const color = colors[i % colors.length];
                viewer.SetMeshesColor([i], color);
              }
            }
            // Definir vista frontal por padrão após carregar o modelo
            setTimeout(() => setModelView('front'), 100);
          }
        });

        // Preparar arquivos para carregamento
        const files = [];
        for (const modelInfo of modelDataArray) {
          const blob = new Blob([modelInfo.data], { type: 'application/octet-stream' });
          const file = new File([blob], `${modelInfo.nome}.stl`, { type: 'application/octet-stream' });
          files.push(file);
        }

        // Carregar todos os modelos de uma vez
        viewer.LoadModelFromFileList(files);

        // Adicionar evento para quando o modelo terminar de carregar
        // Isso garante que a orientação frontal seja aplicada após o carregamento completo
        if (typeof viewer.GetViewer === 'function') {
          const viewerObj = viewer.GetViewer();
          if (viewerObj && typeof viewerObj.AddModelFinishedHandler === 'function') {
            viewerObj.AddModelFinishedHandler(() => {
              // Definir vista frontal após o carregamento completo
              setTimeout(() => setModelView('front'), 200);
            });
          }
        }

        // Adicionar evento de duplo clique para resetar a visualização
        viewerContainer.value.addEventListener('dblclick', () => {
          if (viewer) {
            try {
              // Tentar ajustar o modelo à janela
              // Diferentes versões da biblioteca podem ter métodos diferentes
              if (typeof viewer.FitModelToWindow === 'function') {
                viewer.FitModelToWindow();
              } else if (typeof viewer.GetViewer === 'function') {
                const viewerObj = viewer.GetViewer();
                if (viewerObj && typeof viewerObj.FitToWindow === 'function') {
                  viewerObj.FitToWindow();
                } else if (viewerObj && typeof viewerObj.FitSphereToWindow === 'function') {
                  viewerObj.FitSphereToWindow();
                }
              }
            } catch (error) {
              console.error('Erro ao resetar visualização:', error);
            }
          }
        });

      } catch (error) {
        console.error('Erro ao inicializar visualizador 3D:', error);
        cSwal.cError("Erro ao inicializar o visualizador 3D.");
        emit('error', error);
      }
    };

    // Watcher para controlar visibilidade dos modelos em modo grupo
    watch(() => props.modelosVisiveis, (newVisibility) => {
      if (props.isGroupMode && viewer && props.grupoModelos) {
        props.grupoModelos.forEach((modelo, index) => {
          const isVisible = newVisibility[modelo.id] !== false;
          if (viewer.SetMeshesVisibility) {
            viewer.SetMeshesVisibility([index], isVisible);
          }
        });
      }
    }, { deep: true });

    // Inicializar o visualizador quando o componente for montado
    onMounted(() => {
      // Pequeno delay para garantir que o DOM esteja completamente renderizado
      setTimeout(() => {
        initViewer();
      }, 100);
    });

    // Limpar recursos quando o componente for desmontado
    onBeforeUnmount(() => {
      if (progressInterval) {
        clearInterval(progressInterval);
      }

      if (viewer) {
        viewer.Destroy();
        viewer = null;
      }
    });

    // Função para alternar a exibição de linhas (edges)
    const toggleEdges = () => {
      if (!viewer) return;

      showEdges.value = !showEdges.value;

      try {
        // Recriar as configurações de borda com o novo valor
        const edgeSettings = new OV.EdgeSettings(
          showEdges.value,
          new OV.RGBColor(0, 0, 0),
          1
        );

        // Tentar diferentes abordagens para alternar a exibição de linhas
        if (typeof viewer.GetViewer === 'function') {
          const viewerObj = viewer.GetViewer();
          if (viewerObj) {
            // Tentar com SetEdgeSettings
            if (typeof viewerObj.SetEdgeSettings === 'function') {
              viewerObj.SetEdgeSettings(edgeSettings);
            }
            // Tentar com propriedade direta
            else if (viewerObj.edgeSettings !== undefined) {
              viewerObj.edgeSettings = edgeSettings;
            }
          }
        }
        // Tentar com propriedade direta no viewer
        else if (typeof viewer.SetEdgeSettings === 'function') {
          viewer.SetEdgeSettings(edgeSettings);
        }
      } catch (error) {
        console.error('Erro ao alternar exibição de linhas:', error);
      }
    };

    // Função para alternar o modo de tela cheia
    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value;

      // Pequeno delay para permitir que as classes CSS sejam aplicadas
      setTimeout(() => {
        if (viewer) {
          try {
            // Forçar redimensionamento do canvas
            const canvas = viewerContainer.value.querySelector('canvas');
            if (canvas) {
              // Obter as dimensões do container
              const containerWidth = viewerContainer.value.clientWidth;
              const containerHeight = viewerContainer.value.clientHeight;

              // Aplicar as dimensões ao canvas
              canvas.width = containerWidth;
              canvas.height = containerHeight;
              canvas.style.width = containerWidth + 'px';
              canvas.style.height = containerHeight + 'px';
            }

            // Tentar redimensionar o visualizador usando métodos da API
            if (typeof viewer.Resize === 'function') {
              viewer.Resize();
            }

            // Tentar ajustar o modelo à janela
            // Diferentes versões da biblioteca podem ter métodos diferentes
            if (typeof viewer.FitModelToWindow === 'function') {
              viewer.FitModelToWindow();
            } else if (typeof viewer.GetViewer === 'function') {
              const viewerObj = viewer.GetViewer();
              if (viewerObj) {
                if (typeof viewerObj.FitToWindow === 'function') {
                  viewerObj.FitToWindow();
                } else if (typeof viewerObj.FitSphereToWindow === 'function') {
                  viewerObj.FitSphereToWindow();
                } else if (typeof viewerObj.Resize === 'function') {
                  viewerObj.Resize();
                }
              }
            }
          } catch (error) {
            console.error('Erro ao redimensionar visualizador:', error);
          }
        }
      }, 300); // Aumentar o delay para garantir que as transições CSS estejam completas
    };

    // Função para resetar a visualização
    const resetView = () => {
      if (!viewer) return;

      try {
        // Tentar ajustar o modelo à janela
        // Diferentes versões da biblioteca podem ter métodos diferentes
        if (typeof viewer.FitModelToWindow === 'function') {
          viewer.FitModelToWindow();
        } else if (typeof viewer.GetViewer === 'function') {
          const viewerObj = viewer.GetViewer();
          if (viewerObj) {
            if (typeof viewerObj.FitToWindow === 'function') {
              viewerObj.FitToWindow();
            } else if (typeof viewerObj.FitSphereToWindow === 'function') {
              viewerObj.FitSphereToWindow();
            } else if (typeof viewerObj.ResetCamera === 'function') {
              viewerObj.ResetCamera();
              if (typeof viewerObj.Render === 'function') {
                viewerObj.Render();
              }
            } else {
              // Tentar definir a vista frontal como fallback
              setModelView('front');
            }
          }
        }
      } catch (error) {
        console.error('Erro ao resetar visualização:', error);
        // Tentar definir a vista frontal como fallback em caso de erro
        try {
          setModelView('front');
        } catch (e) {
          console.error('Erro ao definir vista frontal como fallback:', e);
        }
      }
    };

    // Função para rotacionar o modelo
    const rotateModel = (direction) => {
      if (!viewer) return;

      try {
        const viewerObj = viewer.GetViewer();
        if (!viewerObj) return;

        const camera = viewerObj.GetCamera();
        const eye = camera.eye;
        const center = camera.center;
        const up = camera.up;

        // Ângulo de rotação em graus
        const angle = 15;

        // Calcular a direção da câmera para o centro
        const direction3D = {
          x: center.x - eye.x,
          y: center.y - eye.y,
          z: center.z - eye.z
        };

        // Normalizar o vetor
        const length = Math.sqrt(direction3D.x * direction3D.x + direction3D.y * direction3D.y + direction3D.z * direction3D.z);
        direction3D.x /= length;
        direction3D.y /= length;
        direction3D.z /= length;

        // Calcular o vetor direita (perpendicular à direção e ao vetor up)
        const right = {
          x: direction3D.y * up.z - direction3D.z * up.y,
          y: direction3D.z * up.x - direction3D.x * up.z,
          z: direction3D.x * up.y - direction3D.y * up.x
        };

        // Normalizar o vetor direita
        const rightLength = Math.sqrt(right.x * right.x + right.y * right.y + right.z * right.z);
        right.x /= rightLength;
        right.y /= rightLength;
        right.z /= rightLength;

        // Converter ângulo para radianos
        const rad = angle * Math.PI / 180;

        // Calcular a nova posição da câmera com base na direção
        let newEye = { ...eye };

        switch (direction) {
          case 'up':
            // Rotacionar para cima (em torno do eixo direita)
            newEye = rotatePointAroundAxis(eye, center, right, rad);
            break;
          case 'down':
            // Rotacionar para baixo (em torno do eixo direita)
            newEye = rotatePointAroundAxis(eye, center, right, -rad);
            break;
          case 'left':
            // Rotacionar para a esquerda (em torno do eixo up)
            newEye = rotatePointAroundAxis(eye, center, up, rad);
            break;
          case 'right':
            // Rotacionar para a direita (em torno do eixo up)
            newEye = rotatePointAroundAxis(eye, center, up, -rad);
            break;
        }

        // Atualizar a câmera
        const newCamera = new OV.Camera(
          new OV.Coord3D(newEye.x, newEye.y, newEye.z),
          new OV.Coord3D(center.x, center.y, center.z),
          new OV.Coord3D(up.x, up.y, up.z),
          camera.fov
        );

        viewerObj.SetCamera(newCamera);
      } catch (error) {
        console.error('Erro ao rotacionar modelo:', error);
      }
    };

    // Função auxiliar para rotacionar um ponto em torno de um eixo
    const rotatePointAroundAxis = (point, center, axis, angle) => {
      // Transladar o ponto para a origem
      const p = {
        x: point.x - center.x,
        y: point.y - center.y,
        z: point.z - center.z
      };

      // Calcular componentes da fórmula de rotação de Rodrigues
      const cosAngle = Math.cos(angle);
      const sinAngle = Math.sin(angle);

      // Aplicar a fórmula de rotação de Rodrigues
      const result = {
        x: p.x * cosAngle + (axis.y * p.z - axis.z * p.y) * sinAngle + axis.x * (axis.x * p.x + axis.y * p.y + axis.z * p.z) * (1 - cosAngle),
        y: p.y * cosAngle + (axis.z * p.x - axis.x * p.z) * sinAngle + axis.y * (axis.x * p.x + axis.y * p.y + axis.z * p.z) * (1 - cosAngle),
        z: p.z * cosAngle + (axis.x * p.y - axis.y * p.x) * sinAngle + axis.z * (axis.x * p.x + axis.y * p.y + axis.z * p.z) * (1 - cosAngle)
      };

      // Transladar de volta
      return {
        x: result.x + center.x,
        y: result.y + center.y,
        z: result.z + center.z
      };
    };

    // Função para aplicar zoom
    const zoomModel = (direction) => {
      if (!viewer) return;

      try {
        const viewerObj = viewer.GetViewer();
        if (!viewerObj) return;

        const camera = viewerObj.GetCamera();
        const eye = camera.eye;
        const center = camera.center;

        // Calcular a direção da câmera para o centro
        const direction3D = {
          x: center.x - eye.x,
          y: center.y - eye.y,
          z: center.z - eye.z
        };

        // Normalizar o vetor
        const length = Math.sqrt(direction3D.x * direction3D.x + direction3D.y * direction3D.y + direction3D.z * direction3D.z);
        direction3D.x /= length;
        direction3D.y /= length;
        direction3D.z /= length;

        // Fator de zoom
        const zoomFactor = direction === 'in' ? 0.8 : 1.25;

        // Calcular a nova posição da câmera
        const newEye = {
          x: eye.x + (center.x - eye.x) * (1 - zoomFactor),
          y: eye.y + (center.y - eye.y) * (1 - zoomFactor),
          z: eye.z + (center.z - eye.z) * (1 - zoomFactor)
        };

        // Atualizar a câmera
        const newCamera = new OV.Camera(
          new OV.Coord3D(newEye.x, newEye.y, newEye.z),
          new OV.Coord3D(center.x, center.y, center.z),
          new OV.Coord3D(camera.up.x, camera.up.y, camera.up.z),
          camera.fov
        );

        viewerObj.SetCamera(newCamera);
      } catch (error) {
        console.error('Erro ao aplicar zoom:', error);
      }
    };

    // Função para definir uma vista específica
    const setModelView = (view) => {
      if (!viewer) return;

      try {
        const viewerObj = viewer.GetViewer();
        if (!viewerObj) return;

        // Obter a câmera atual
        const camera = viewerObj.GetCamera();
        const center = camera.center;

        // Calcular a distância atual da câmera ao centro
        const eye = camera.eye;
        const distance = Math.sqrt(
          Math.pow(eye.x - center.x, 2) +
          Math.pow(eye.y - center.y, 2) +
          Math.pow(eye.z - center.z, 2)
        );

        // Definir a nova posição da câmera com base na vista selecionada
        let newEye;
        let newUp;

        switch (view) {
          case 'front':
            newEye = new OV.Coord3D(center.x, center.y, center.z + distance);
            newUp = new OV.Coord3D(0, 1, 0);
            break;
          case 'side':
            newEye = new OV.Coord3D(center.x + distance, center.y, center.z);
            newUp = new OV.Coord3D(0, 1, 0);
            break;
          case 'top':
            newEye = new OV.Coord3D(center.x, center.y + distance, center.z);
            newUp = new OV.Coord3D(0, 0, 1);
            break;
        }

        // Atualizar a câmera
        const newCamera = new OV.Camera(
          newEye,
          new OV.Coord3D(center.x, center.y, center.z),
          newUp,
          camera.fov
        );

        viewerObj.SetCamera(newCamera);
      } catch (error) {
        console.error('Erro ao definir vista:', error);
      }
    };

    // Função para fechar o visualizador
    const closeViewer = () => {
      // Emitir evento close
      emit('close');

      // Fechar o modal diretamente (fallback)
      try {
        const modalElement = document.getElementById('modelViewerModal');
        if (modalElement) {
          // Esconder o modal com animação
          modalElement.classList.remove('show');

          // Remover backdrop com animação
          const backdrop = document.querySelector('.modal-backdrop');
          if (backdrop) {
            backdrop.classList.remove('show');
          }

          // Aguardar a animação terminar antes de remover completamente
          setTimeout(() => {
            modalElement.style.display = 'none';
            modalElement.removeAttribute('aria-modal');
            modalElement.removeAttribute('role');
            document.body.classList.remove('modal-open');

            // Remover backdrop do DOM
            if (backdrop && backdrop.parentNode) {
              backdrop.parentNode.removeChild(backdrop);
            }
          }, 150);
        }
      } catch (error) {
        console.error('Erro ao fechar modal:', error);
      }
    };

    return {
      viewerContainer,
      isLoading,
      loadingProgress,
      isFullscreen,
      showEdges,
      toggleEdges,
      toggleFullscreen,
      resetView,
      rotateModel,
      zoomModel,
      setModelView,
      closeViewer
    };
  }
};
</script>

<style scoped>
.modelo3d-viewer-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.modelo3d-viewer-container {
  position: relative;
  width: 100%;
  height: 500px;
  background: linear-gradient(135deg, #b0b8c8, #9098b0);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Efeito de brilho radial */
.modelo3d-viewer-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.5) 0%,
    rgba(255, 255, 255, 0.15) 30%,
    rgba(180, 180, 190, 0) 70%
  );
  pointer-events: none;
  z-index: 1;
}

.online-3d-viewer {
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  border-radius: 8px;
  overflow: hidden;
}

.online-3d-viewer.hidden {
  opacity: 0;
}

/* Modo de tela cheia */
.fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: radial-gradient(
    circle at center,
    rgba(50, 50, 60, 0.8) 0%,
    rgba(20, 20, 30, 0.97) 100%
  );
  z-index: 1;
  pointer-events: none;
  animation: fadeIn 0.3s ease;
}

.online-3d-viewer.fullscreen-mode {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  height: 90vh;
  z-index: 1100;
  border-radius: 16px;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.4);
  animation: scaleIn 0.3s ease;
  background: linear-gradient(135deg, #a0a8b8, #8088a0);
}

/* Efeito de brilho radial para o modo tela cheia */
.online-3d-viewer.fullscreen-mode::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.5) 0%,
    rgba(255, 255, 255, 0.2) 25%,
    rgba(220, 220, 225, 0) 60%
  );
  pointer-events: none;
  z-index: 1;
  border-radius: 16px;
}

/* Botão para sair do modo tela cheia */
.exit-fullscreen-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1200;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  color: #3182ce;
  font-size: 1.1rem;
  transform: scale(1);
}

.exit-fullscreen-btn:hover {
  background-color: white;
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
}

.exit-fullscreen-btn:active {
  transform: scale(0.95);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: translate(-50%, -50%) scale(0.9); opacity: 0; }
  to { transform: translate(-50%, -50%) scale(1); opacity: 1; }
}

/* Loader */
.stl-loader-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(240, 240, 240, 0.8);
  z-index: 10;
}

.stl-loader-spinner {
  margin-bottom: 20px;
}

.stl-loader-progress {
  width: 80%;
  max-width: 400px;
  text-align: center;
}

.stl-loader-text {
  margin-top: 10px;
  font-weight: 500;
  color: #0d6efd;
}

.progress {
  height: 10px;
  border-radius: 5px;
  background-color: #e9ecef;
  margin-bottom: 5px;
}

/* Controles do visualizador */
.viewer-controls {
  position: absolute;
  bottom: 15px;
  left: 15px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.controls-group {
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.controls-group:hover {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.18);
  transform: translateY(-2px);
}

.horizontal-controls {
  display: flex;
  gap: 5px;
}

.control-btn {
  width: 38px;
  height: 38px;
  border-radius: 8px;
  border: 1px solid rgba(200, 200, 220, 0.5);
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #4a5568;
  position: relative;
  overflow: hidden;
  font-size: 0.9rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.control-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.control-btn:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  color: #2d3748;
}

.control-btn:active::after {
  transform: translate(-50%, -50%) scale(2);
  opacity: 1;
  transition: 0s;
}

.control-btn.active {
  background: linear-gradient(135deg, #e6f0ff, #d4e6ff);
  color: #3182ce;
  border-color: #bee3f8;
  box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.2);
}

.main-controls {
  flex-direction: row;
  gap: 5px;
}

.zoom-controls {
  flex-direction: row;
  gap: 5px;
  justify-content: center;
}

.view-controls {
  flex-direction: row;
  gap: 5px;
}

/* Card de informações do modelo */
.modelo-info-card {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 10;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.info-card-content {
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.6);
  max-width: 280px;
  transform: translateY(0);
}

.info-card-header {
  background: linear-gradient(135deg, #3182ce, #2c5282);
  color: white;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.info-icon {
  font-size: 1rem;
}

.info-title {
  font-size: 0.95rem;
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info-card-body {
  padding: 10px 15px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  font-size: 0.85rem;
  color: #4a5568;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon-small {
  font-size: 0.8rem;
  width: 16px;
  color: #3182ce;
}

/* Instruções de uso */
.viewer-instructions {
  position: absolute;
  bottom: 15px;
  right: 15px;
  z-index: 10;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.instructions-content {
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 10px;
  padding: 12px 18px;
  font-size: 0.8rem;
  color: #2d3748;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
  line-height: 1.6;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.6);
  max-width: 220px;
}

.instructions-content strong {
  color: #3182ce;
  font-weight: 600;
}

/* Botão de voltar para mobile */
.mobile-back-btn {
  display: none;
  position: absolute;
  bottom: 25px;
  left: 50%;
  transform: translateX(-50%);
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  background-color: rgba(255, 255, 255, 0.95);
  color: #3182ce;
  font-size: 1.3rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  z-index: 1300; /* Valor alto para garantir que fique acima de tudo */
  cursor: pointer;
  transition: all 0.2s ease;
  align-items: center;
  justify-content: center;
}

.mobile-back-btn:hover, .mobile-back-btn:active {
  background-color: white;
  transform: translateX(-50%) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

/* Responsividade */
@media (max-width: 768px) {
  .modelo3d-viewer-wrapper {
    background: linear-gradient(135deg, #b0b8c8, #9098b0);
    border-radius: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1050;
  }

  /* Ajustar altura do container para telas menores */
  .modelo3d-viewer-container {
    height: 100%;
    width: 100%;
    border-radius: 0;
    box-shadow: none;
    margin: 0;
  }

  /* Esconder controles desktop */
  .desktop-controls {
    display: none;
  }

  /* Mostrar botão de voltar */
  .mobile-back-btn {
    display: flex;
  }

  /* Esconder instruções de navegação em dispositivos móveis */
  .viewer-instructions {
    display: none;
  }

  /* Ajustar card de informações */
  .modelo-info-card {
    top: 15px;
    right: 15px;
    z-index: 20;
  }

  .modelo-info-card .info-card-content {
    max-width: 200px;
    opacity: 0.95;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25);
  }

  .info-card-header {
    padding: 8px 12px;
  }

  .info-title {
    font-size: 0.85rem;
  }

  .info-card-body {
    padding: 8px 12px;
  }

  .info-item {
    font-size: 0.75rem;
    margin-bottom: 4px;
    gap: 6px;
  }

  .info-icon-small {
    font-size: 0.75rem;
    width: 16px;
  }

  /* Ajustar o fundo do visualizador para combinar com o modal */
  .online-3d-viewer {
    background: linear-gradient(135deg, #b0b8c8, #9098b0);
  }
}

/* Ajustes para telas muito pequenas (smartphones) */
@media (max-width: 480px) {
  .modelo-info-card .info-card-content {
    max-width: 160px;
  }

  .mobile-back-btn {
    bottom: 15px;
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }

  .info-card-header {
    padding: 6px 10px;
  }

  .info-title {
    font-size: 0.8rem;
  }

  .info-card-body {
    padding: 6px 10px;
  }

  .info-item {
    font-size: 0.7rem;
    margin-bottom: 3px;
  }
}
</style>
