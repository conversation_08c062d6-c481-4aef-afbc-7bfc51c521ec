import axios from '@/services/axios';

/**
 * Busca todos os históricos de ações com filtros opcionais
 * @param {Object} filters - Filtros para a busca
 * @returns {Promise<Object>} - Resposta com dados paginados
 */
export async function getActionHistory(filters = {}) {
  try {
    const params = new URLSearchParams();

    // Mapear filtros do frontend para o backend
    if (filters.date_from && filters.date_to) {
      params.append('start_date', filters.date_from);
      params.append('end_date', filters.date_to);
    }

    if (filters.action_type) {
      params.append('action_type', filters.action_type);
    }

    if (filters.entity_type) {
      params.append('entity_type', filters.entity_type);
    }

    if (filters.user_search) {
      params.append('user_search', filters.user_search);
    }

    if (filters.patient_id) {
      params.append('patient_id', filters.patient_id);
    }

    if (filters.dentist_id) {
      params.append('dentist_id', filters.dentist_id);
    }

    if (filters.user_id) {
      params.append('user_id', filters.user_id);
    }

    // Paginação
    if (filters.page) {
      params.append('page', filters.page);
    }

    if (filters.per_page) {
      params.append('per_page', filters.per_page);
    }

    // Ordenação
    if (filters.sort_by) {
      params.append('sort_by', filters.sort_by);
    }

    if (filters.sort_type) {
      params.append('sort_type', filters.sort_type);
    }

    const response = await axios.get(`/action-history?${params.toString()}`);

    if (!response || !response.data) {
      return { data: [], total: 0, current_page: 1, last_page: 1 };
    }

    return response.data;
  } catch (error) {
    console.error("Erro ao buscar histórico de ações:", error);
    throw error;
  }
}

/**
 * Busca estatísticas do histórico de ações
 * @returns {Promise<Object>} - Estatísticas do histórico
 */
export async function getActionHistoryStats() {
  try {
    const response = await axios.get('/action-history/stats');

    if (!response || !response.data) {
      return {};
    }

    return response.data;
  } catch (error) {
    console.error("Erro ao buscar estatísticas do histórico:", error);
    throw error;
  }
}

/**
 * Exporta histórico de ações
 * @param {Object} filters - Filtros para exportação
 * @param {string} format - Formato de exportação (csv, excel)
 * @returns {Promise<Blob>} - Arquivo para download
 */
export async function exportActionHistory(filters = {}, format = 'csv') {
  try {
    const params = new URLSearchParams();

    // Mapear filtros do frontend para o backend
    if (filters.date_from && filters.date_to) {
      params.append('start_date', filters.date_from);
      params.append('end_date', filters.date_to);
    }

    if (filters.action_type) {
      params.append('action_type', filters.action_type);
    }

    if (filters.entity_type) {
      params.append('entity_type', filters.entity_type);
    }

    if (filters.user_search) {
      params.append('user_search', filters.user_search);
    }

    params.append('format', format);

    const response = await axios.get(`/action-history/export?${params.toString()}`, {
      responseType: 'blob'
    });

    return response.data;
  } catch (error) {
    console.error("Erro ao exportar histórico:", error);
    throw error;
  }
}

/**
 * Busca histórico de ações por paciente
 * @param {number} patientId - ID do paciente
 * @param {Object} filters - Filtros adicionais
 * @returns {Promise<Object>} - Histórico do paciente
 */
export async function getActionHistoryByPatient(patientId, filters = {}) {
  try {
    const params = new URLSearchParams();

    Object.keys(filters).forEach(key => {
      if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
        params.append(key, filters[key]);
      }
    });

    const response = await axios.get(`/action-history/patient/${patientId}?${params.toString()}`);

    if (!response || !response.data) {
      return { data: [], total: 0 };
    }

    return response.data;
  } catch (error) {
    console.error("Erro ao buscar histórico do paciente:", error);
    throw error;
  }
}

/**
 * Busca histórico de ações por dentista
 * @param {number} dentistId - ID do dentista
 * @param {Object} filters - Filtros adicionais
 * @returns {Promise<Object>} - Histórico do dentista
 */
export async function getActionHistoryByDentist(dentistId, filters = {}) {
  try {
    const params = new URLSearchParams();

    Object.keys(filters).forEach(key => {
      if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
        params.append(key, filters[key]);
      }
    });

    const response = await axios.get(`/action-history/dentist/${dentistId}?${params.toString()}`);

    if (!response || !response.data) {
      return { data: [], total: 0 };
    }

    return response.data;
  } catch (error) {
    console.error("Erro ao buscar histórico do dentista:", error);
    throw error;
  }
}

/**
 * Busca histórico de ações por usuário
 * @param {number} userId - ID do usuário
 * @param {Object} filters - Filtros adicionais
 * @returns {Promise<Object>} - Histórico do usuário
 */
export async function getActionHistoryByUser(userId, filters = {}) {
  try {
    const params = new URLSearchParams();

    Object.keys(filters).forEach(key => {
      if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
        params.append(key, filters[key]);
      }
    });

    const response = await axios.get(`/action-history/user/${userId}?${params.toString()}`);

    if (!response || !response.data) {
      return { data: [], total: 0 };
    }

    return response.data;
  } catch (error) {
    console.error("Erro ao buscar histórico do usuário:", error);
    throw error;
  }
}

/**
 * Busca um registro específico do histórico
 * @param {number} id - ID do registro
 * @returns {Promise<Object>} - Dados do registro
 */
export async function getActionHistoryRecord(id) {
  try {
    const response = await axios.get(`/action-history/${id}`);

    if (!response || !response.data) {
      return null;
    }

    return response.data;
  } catch (error) {
    console.error("Erro ao buscar registro do histórico:", error);
    throw error;
  }
}

export default {
  getActionHistory,
  getActionHistoryStats,
  exportActionHistory,
  getActionHistoryByPatient,
  getActionHistoryByDentist,
  getActionHistoryByUser,
  getActionHistoryRecord
};
