<template>
  <div class="action-history-container p-3">
    <!-- Filtros -->
    <div class="card mb-4">
      <div class="p-3 pb-0 card-header">
        <div class="row">
          <div class="col-md-8 d-flex align-items-center">
            <h6 class="mb-0">Filt<PERSON> de Pesquisa</h6>
          </div>
          <div class="col-md-4 text-end">
            <button
              class="btn btn-sm btn-outline-secondary"
              @click="toggleFilters"
            >
              <font-awesome-icon :icon="['fas', showFilters ? 'chevron-up' : 'chevron-down']" />
              {{ showFilters ? 'Ocultar' : 'Mostrar' }} Filtros
            </button>
          </div>
        </div>
      </div>
      <div class="card-body" v-show="showFilters">
        <div class="row">
          <!-- Filtro por Data -->
          <div class="col-md-3 mb-3">
            <label class="form-label">Data Inicial</label>
            <input
              type="date"
              class="form-control"
              v-model="filters.date_from"
              @change="applyFilters"
            />
          </div>
          <div class="col-md-3 mb-3">
            <label class="form-label">Data Final</label>
            <input
              type="date"
              class="form-control"
              v-model="filters.date_to"
              @change="applyFilters"
            />
          </div>

          <!-- Filtro por Tipo de Ação -->
          <div class="col-md-3 mb-3">
            <label class="form-label">Tipo de Ação</label>
            <select
              class="form-control"
              v-model="filters.action_type"
              @change="applyFilters"
            >
              <option value="">Todos os tipos</option>
              <option value="create">Criação</option>
              <option value="update">Atualização</option>
              <option value="delete">Exclusão</option>
              <option value="login">Login</option>
              <option value="logout">Logout</option>
              <option value="export">Exportação</option>
            </select>
          </div>

          <!-- Filtro por Entidade -->
          <div class="col-md-3 mb-3">
            <label class="form-label">Entidade</label>
            <select
              class="form-control"
              v-model="filters.entity_type"
              @change="applyFilters"
            >
              <option value="">Todas as entidades</option>
              <option value="App\Models\Paciente">Paciente</option>
              <option value="App\Models\Dentista">Dentista</option>
              <option value="App\Models\Clinica">Clínica</option>
              <option value="App\Models\Consulta">Consulta</option>
              <option value="App\Models\User">Usuário</option>
            </select>
          </div>
        </div>

        <div class="row">
          <!-- Filtro por Usuário -->
          <div class="col-md-6 mb-3">
            <label class="form-label">Usuário</label>
            <input
              type="text"
              class="form-control"
              placeholder="Nome do usuário..."
              v-model="filters.user_search"
              @input="debounceSearch"
            />
          </div>

          <!-- Botões de Ação -->
          <div class="col-md-6 mb-3 d-flex align-items-end">
            <button
              class="btn btn-primary me-2"
              @click="applyFilters"
              :disabled="loading"
            >
              <font-awesome-icon :icon="['fas', 'search']" class="me-1" />
              Pesquisar
            </button>
            <button
              class="btn btn-outline-secondary me-2"
              @click="clearFilters"
            >
              <font-awesome-icon :icon="['fas', 'times']" class="me-1" />
              Limpar
            </button>
            <button
              class="btn btn-success"
              @click="exportData"
              :disabled="loading"
            >
              <font-awesome-icon :icon="['fas', 'download']" class="me-1" />
              Exportar
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Estatísticas -->
    <div class="row mb-1" v-if="stats && Object.keys(stats).length > 0">
      <div class="col-md-3" v-for="(stat, key) in stats" :key="key">
        <div class="card">
          <div class="card-body text-center py-3">
            <h5 class="card-title text-primary mb-1">{{ stat.value }}</h5>
            <p class="card-text text-muted mb-0 small">{{ stat.label }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabela -->
    <div class="card">
      <div class="p-3 pb-0 card-header">
        <div class="row">
          <div class="col-md-8 d-flex align-items-center">
            <h6 class="mb-0">Histórico de Ações</h6>
          </div>
          <div class="col-md-4 text-end">
            <span class="text-muted">{{ totalRecords }} registro(s) encontrado(s)</span>
          </div>
        </div>
      </div>
      <div class="card-body p-0">
        <div v-if="loading" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Carregando...</span>
          </div>
          <p class="mt-2 text-muted">Carregando histórico...</p>
        </div>

        <div v-else-if="actionHistory.length === 0" class="text-center py-4">
          <font-awesome-icon :icon="['fas', 'inbox']" class="text-muted mb-3" size="3x" />
          <p class="text-muted">Nenhum registro encontrado com os filtros aplicados.</p>
        </div>

        <EasyDataTable
          v-else
          :headers="headers"
          :items="actionHistory"
          :server-options="serverOptions"
          :server-items-length="totalRecords"
          @update:server-options="updateServerOptions"
          body-row-class-name="action-history-row"
          header-item-class-name="table-header-item"
          body-item-class-name="table-body-item"
          rows-per-page-message="Registros por página"
          rows-of-page-separator-message="de"
          empty-message="Sem registros"
          :loading="loading"
        >
          <!-- Data/Hora -->
          <template #item-created_at="{ created_at }">
            <div class="d-flex flex-column">
              <span class="text-sm font-weight-bold">{{ $filters.date(created_at) }}</span>
              <span class="text-xs text-muted">{{ $filters.time(created_at) }}</span>
            </div>
          </template>

          <!-- Usuário -->
          <template #item-user="{ user }">
            <div class="d-flex flex-column">
              <span class="text-sm font-weight-bold">{{ user?.nome || user?.username || 'N/A' }}</span>
              <span class="text-xs text-muted">{{ user?.email || '' }}</span>
            </div>
          </template>

          <!-- Tipo de Ação -->
          <template #item-action_type="{ action_type }">
            <span :class="getActionTypeClass(action_type)">
              {{ getActionTypeLabel(action_type) }}
            </span>
          </template>

          <!-- Entidade -->
          <template #item-entity="{ entity_type, patient, dentist, clinica }">
            <div class="d-flex flex-column">
              <span class="text-sm font-weight-bold">{{ getEntityLabel(entity_type) }}</span>
              <span class="text-xs text-muted">
                {{ getEntityName(entity_type, patient, dentist, clinica) }}
              </span>
            </div>
          </template>

          <!-- Descrição -->
          <template #item-action_description="{ action_description }">
            <span class="text-sm" :title="action_description">
              {{ truncateText(action_description, 50) }}
            </span>
          </template>

          <!-- Ações -->
          <template #item-actions="{ id }">
            <div class="d-flex justify-content-center align-items-center h-100">
              <button
                class="btn btn-sm btn-outline-info"
                @click="viewDetails(id)"
                title="Ver detalhes"
              >
                <font-awesome-icon :icon="['fas', 'eye']" />
              </button>
            </div>
          </template>
        </EasyDataTable>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getActionHistory,
  getActionHistoryStats,
  exportActionHistory,
  getActionHistoryRecord
} from '@/services/actionHistoryService';
import cSwal from '@/utils/cSwal.js';

export default {
  name: 'ActionHistoryTable',
  data() {
    return {
      actionHistory: [],
      loading: false,
      showFilters: true,
      totalRecords: 0,
      stats: {},
      searchTimeout: null,

      // Filtros
      filters: {
        date_from: '',
        date_to: '',
        action_type: '',
        entity_type: '',
        user_search: '',
        page: 1,
        per_page: 15
      },

      // Configuração do servidor para EasyDataTable
      serverOptions: {
        page: 1,
        rowsPerPage: 15,
        sortBy: 'created_at',
        sortType: 'desc'
      },

      // Headers da tabela
      headers: [
        { text: "DATA/HORA", value: "created_at", sortable: true, width: 140 },
        { text: "USUÁRIO", value: "user", sortable: false, width: 180 },
        { text: "AÇÃO", value: "action_type", sortable: true, width: 120 },
        { text: "ENTIDADE", value: "entity", sortable: false, width: 150 },
        { text: "DESCRIÇÃO", value: "action_description", sortable: false, width: 250 },
        { text: "AÇÕES", value: "actions", sortable: false, width: 80 }
      ]
    };
  },

  async mounted() {
    await this.loadStats();
    await this.loadActionHistory();
  },

  methods: {
    async loadActionHistory() {
      this.loading = true;
      try {
        const searchFilters = {
          ...this.filters,
          page: this.serverOptions.page,
          per_page: this.serverOptions.rowsPerPage,
          sort_by: this.serverOptions.sortBy,
          sort_type: this.serverOptions.sortType
        };

        const response = await getActionHistory(searchFilters);

        if (response && response.data) {
          this.actionHistory = response.data;
          this.totalRecords = response.total || 0;
        } else {
          this.actionHistory = [];
          this.totalRecords = 0;
        }
      } catch (error) {
        console.error('Erro ao carregar histórico:', error);
        cSwal.cError('Erro ao carregar histórico de ações');
        this.actionHistory = [];
        this.totalRecords = 0;
      } finally {
        this.loading = false;
      }
    },

    async loadStats() {
      try {
        const response = await getActionHistoryStats();
        if (response) {
          this.stats = {
            total: { value: response.total_actions || 0, label: 'Total de Ações' },
            today: { value: response.actions_today || 0, label: 'Ações Hoje' },
            users: { value: response.active_users || 0, label: 'Usuários Ativos' },
            entities: { value: response.entities_affected || 0, label: 'Entidades Afetadas' }
          };
        }
      } catch (error) {
        console.error('Erro ao carregar estatísticas:', error);
      }
    },

    async updateServerOptions(newOptions) {
      this.serverOptions = { ...newOptions };
      await this.loadActionHistory();
    },

    async applyFilters() {
      this.serverOptions.page = 1; // Reset para primeira página
      await this.loadActionHistory();
    },

    clearFilters() {
      this.filters = {
        date_from: '',
        date_to: '',
        action_type: '',
        entity_type: '',
        user_search: '',
        page: 1,
        per_page: 15
      };
      this.applyFilters();
    },

    debounceSearch() {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }
      this.searchTimeout = setTimeout(() => {
        this.applyFilters();
      }, 500);
    },

    toggleFilters() {
      this.showFilters = !this.showFilters;
    },

    async exportData() {
      try {
        cSwal.loading('Preparando exportação...');

        const blob = await exportActionHistory(this.filters, 'csv');

        // Criar URL para download
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `historico-acoes-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        cSwal.loaded();
        cSwal.cSuccess('Arquivo exportado com sucesso!');
      } catch (error) {
        console.error('Erro ao exportar:', error);
        cSwal.loaded();
        cSwal.cError('Erro ao exportar dados');
      }
    },

    async viewDetails(id) {
      try {
        cSwal.loading('Carregando detalhes...');

        const record = await getActionHistoryRecord(id);

        if (record) {
          const details = this.formatRecordDetails(record);

          cSwal.loaded();
          cSwal.cInfo(details, 'Detalhes do Registro');
        } else {
          cSwal.loaded();
          cSwal.cError('Registro não encontrado');
        }
      } catch (error) {
        console.error('Erro ao carregar detalhes:', error);
        cSwal.loaded();
        cSwal.cError('Erro ao carregar detalhes do registro');
      }
    },

    // Métodos auxiliares para formatação
    formatRecordDetails(record) {
      let details = `<div class="text-left">`;
      details += `<p><strong>Data/Hora:</strong> ${this.$filters.dateTime(record.created_at)}</p>`;
      details += `<p><strong>Usuário:</strong> ${record.user?.nome || record.user?.username || 'N/A'}</p>`;
      details += `<p><strong>Ação:</strong> ${this.getActionTypeLabel(record.action_type)}</p>`;
      details += `<p><strong>Descrição:</strong> ${record.action_description}</p>`;

      if (record.entity_type) {
        details += `<p><strong>Entidade:</strong> ${this.getEntityLabel(record.entity_type)}</p>`;
      }

      if (record.ip_address) {
        details += `<p><strong>IP:</strong> ${record.ip_address}</p>`;
      }

      if (record.old_data || record.new_data) {
        details += `<hr>`;
        if (record.old_data) {
          details += `<p><strong>Dados Anteriores:</strong></p>`;
          try {
            const oldData = typeof record.old_data === 'string' ? JSON.parse(record.old_data) : record.old_data;
            details += `<pre class="text-xs">${JSON.stringify(oldData, null, 2)}</pre>`;
          } catch (e) {
            details += `<pre class="text-xs">${record.old_data}</pre>`;
          }
        }
        if (record.new_data) {
          details += `<p><strong>Dados Novos:</strong></p>`;
          try {
            const newData = typeof record.new_data === 'string' ? JSON.parse(record.new_data) : record.new_data;
            details += `<pre class="text-xs">${JSON.stringify(newData, null, 2)}</pre>`;
          } catch (e) {
            details += `<pre class="text-xs">${record.new_data}</pre>`;
          }
        }
      }

      details += `</div>`;
      return details;
    },

    getActionTypeLabel(type) {
      const labels = {
        'create': 'Criação',
        'update': 'Atualização',
        'delete': 'Exclusão',
        'login': 'Login',
        'logout': 'Logout',
        'export': 'Exportação',
        'view': 'Visualização',
        'import': 'Importação'
      };
      return labels[type] || type;
    },

    getActionTypeClass(type) {
      const classes = {
        'create': 'badge bg-success',
        'update': 'badge bg-warning',
        'delete': 'badge bg-danger',
        'login': 'badge bg-info',
        'logout': 'badge bg-secondary',
        'export': 'badge bg-primary',
        'view': 'badge bg-light text-dark',
        'import': 'badge bg-dark'
      };
      return classes[type] || 'badge bg-secondary';
    },



    getEntityLabel(type) {
      if (!type) return 'N/A';

      // Remover prefixo App\Models\ se existir
      const cleanType = type.replace(/^App\\Models\\/, '');

      const labels = {
        'Patient': 'Paciente',
        'Dentist': 'Dentista',
        'Dentista': 'Dentista',
        'Clinic': 'Clínica',
        'Clinica': 'Clínica',
        'Consultation': 'Consulta',
        'Consulta': 'Consulta',
        'User': 'Usuário',
        'Usuario': 'Usuário',
        'Appointment': 'Agendamento',
        'Treatment': 'Tratamento',
        'patient': 'Paciente',
        'dentist': 'Dentista',
        'clinic': 'Clínica',
        'consultation': 'Consulta',
        'user': 'Usuário',
        'appointment': 'Agendamento',
        'treatment': 'Tratamento'
      };

      return labels[cleanType] || cleanType;
    },

    getEntityName(entityType, patient, dentist, clinica) {
      if (entityType === 'patient' && patient) {
        return patient.nome || `ID: ${patient.id}`;
      }
      if (entityType === 'dentist' && dentist) {
        return dentist.nome || `ID: ${dentist.id}`;
      }
      if (entityType === 'clinic' && clinica) {
        return clinica.nome || `ID: ${clinica.id}`;
      }
      return 'N/A';
    },

    truncateText(text, maxLength) {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + '...';
    }
  }
};
</script>

<style scoped>
.action-history-container {
  padding: 0;
}

.card {
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  border: none;
  margin-bottom: 1.5rem;
}

.card-header {
  background: #e9ecef;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 10px 10px 0 0 !important;
}

.card-header h6 {
  color: #495057;
  font-weight: 600;
}

.form-control {
  border-radius: 6px;
  border: 1px solid #e3e6f0;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(310deg, #7928ca 0%, #ff0080 100%);
  border: none;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(121, 40, 202, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  border: none;
}

.btn-outline-secondary {
  border-color: #e3e6f0;
  color: #5a5c69;
}

.btn-outline-secondary:hover {
  background-color: #f8f9fc;
  border-color: #d1d3e2;
}

.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
}

.action-history-row {
  transition: all 0.2s ease;
}

.action-history-row:hover {
  background-color: #f8f9fc !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-header-item {
  background-color: #f8f9fc;
  font-weight: 600;
  color: #5a5c69;
  border-bottom: 2px solid #e3e6f0;
}

.table-body-item {
  border-bottom: 1px solid #e3e6f0;
  vertical-align: middle;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

pre {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.75rem;
  max-height: 200px;
  overflow-y: auto;
}
</style>