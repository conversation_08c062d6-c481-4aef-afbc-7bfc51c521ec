<template>
  <div class="tratamento-content">
    <div class="row border-between">
      <div class="col-12">
        <div class="modelo3d-group-container"
          v-show="pendingModeloPreviews.length === 0"
          :class="{ 'drag-over': dragOverContainer }"
          @dragover.prevent="onDragOverContainer"
          @dragleave.prevent="onDragLeaveContainer"
          @drop.prevent="onDropContainer"
        >
          <!-- Indicador de carregamento -->
          <div v-if="isLoadingModelos" class="loading-models-container">
            <div class="loading-spinner">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Carregando...</span>
              </div>
            </div>
            <p class="loading-text">Carregando modelos 3D...</p>
          </div>

          <!-- Centralized no-models message -->
          <div v-else-if="safePatientModelos.length === 0" class="no-models-message">
            <div class="message-content">
              <font-awesome-icon :icon="['fas', 'info-circle']" class="me-2" size="lg" />
              <p class="mb-0">
                Aqui você pode armazenar os modelos 3D do paciente.<br>
                Envie arquivos STL para visualização.
              </p>
            </div>
          </div>

          <!-- Agrupado por data -->
          <template v-else-if="groupByDate">
            <div v-for="group in groupedModelosByDate" :key="group.date" class="date-group">
              <div class="date-group-header">
                <div class="date-group-header-content">
                  <span class="date-how-much">{{ $filters.howMuchTime(group.date, { type: 'date' }) }}</span>
                  <span class="date-text">{{ $filters.dateLong(group.date) }}</span>
                </div>
                <div class="date-group-actions">
                  <button
                    class="date-delete-all-btn"
                    @click.stop="confirmarExcluirTodosModelos(group.date)"
                    title="Excluir todos os modelos desta data"
                  >
                    <i class="fas fa-trash-alt"></i>
                  </button>
                </div>
              </div>
              <div class="models-container w-100">
                <div v-for="modelo in group.modelos" :key="modelo.url" class="modelo-card"
                  draggable="true"
                  @dragstart="onModeloDragStart($event, modelo)"
                >
                  <div class="modelo-preview" @click="openModelViewer(modelo)">
                    <font-awesome-icon :icon="['fas', 'cube']" size="3x" class="modelo-icon" />
                    <div class="modelo-info">
                      <span class="modelo-name">{{ modelo.descricao || 'Modelo 3D' }}</span>
                    </div>
                    <button
                      class="modelo-delete-btn"
                      @click.stop="confirmarExcluirModelo(modelo)"
                      title="Excluir este modelo"
                    >
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- Com agrupamento inteligente -->
          <template v-else-if="enableGrouping">
            <!-- Grupos de modelos como cards únicos clicáveis -->
            <div class="models-container w-100">
              <div v-for="grupo in grupos" :key="grupo.nome" class="modelo-card grupo-card"
                draggable="true"
                @dragstart="onGrupoDragStart($event, grupo)"
                @click="openGrupoViewer(grupo)"
              >
                <div class="modelo-preview grupo-preview">
                  <div class="grupo-badge">
                    <font-awesome-icon :icon="['fas', 'layer-group']" class="me-1" />
                    GRUPO
                  </div>
                  <font-awesome-icon :icon="['fas', 'cubes']" size="3x" class="modelo-icon grupo-icon-main" />
                  <div class="modelo-info">
                    <span class="modelo-name grupo-name">{{ grupo.nome }}</span>
                    <span class="modelo-date grupo-info">{{ grupo.modelos.length }} modelo{{ grupo.modelos.length !== 1 ? 's' : '' }}</span>
                  </div>
                  <div class="grupo-models-preview">
                    <div class="mini-models">
                      <div v-for="(modelo, index) in grupo.modelos.slice(0, 3)" :key="modelo.id" class="mini-model">
                        <font-awesome-icon :icon="['fas', 'cube']" />
                      </div>
                      <div v-if="grupo.modelos.length > 3" class="mini-model-more">
                        +{{ grupo.modelos.length - 3 }}
                      </div>
                    </div>
                  </div>
                  <button
                    class="modelo-delete-btn grupo-delete-btn"
                    @click.stop="confirmarExcluirGrupo(grupo)"
                    title="Excluir este grupo"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Modelos avulsos (sem grupo) -->
            <div v-if="modelosAvulsos.length > 0" class="modelos-avulsos-container">
              <div class="avulsos-header">
                <font-awesome-icon :icon="['fas', 'cube']" class="me-2" />
                <span>Modelos Individuais</span>
                <span class="grupo-count">({{ modelosAvulsos.length }} modelo{{ modelosAvulsos.length !== 1 ? 's' : '' }})</span>
              </div>
              <div class="models-container w-100">
                <div v-for="modelo in modelosAvulsos" :key="modelo.url" class="modelo-card"
                  :class="{ 'modelo-oculto': !isModeloVisivel(modelo.id) }"
                  draggable="true"
                  @dragstart="onModeloDragStart($event, modelo)"
                >
                  <div class="modelo-preview" @click="openModelViewer(modelo)">
                    <div class="modelo-visibility-toggle">
                      <button
                        class="btn btn-sm modelo-eye-btn"
                        :class="isModeloVisivel(modelo.id) ? 'btn-primary' : 'btn-outline-secondary'"
                        @click.stop="toggleModeloVisibilidade(modelo.id)"
                        :title="isModeloVisivel(modelo.id) ? 'Ocultar modelo' : 'Mostrar modelo'"
                      >
                        <font-awesome-icon :icon="['fas', isModeloVisivel(modelo.id) ? 'eye' : 'eye-slash']" />
                      </button>
                    </div>
                    <font-awesome-icon :icon="['fas', 'cube']" size="3x" class="modelo-icon" />
                    <div class="modelo-info">
                      <span class="modelo-name">{{ modelo.descricao || 'Modelo 3D' }}</span>
                      <span class="modelo-date">{{ $filters.dateDmy(modelo.data) }}</span>
                    </div>
                    <button
                      class="modelo-delete-btn"
                      @click.stop="confirmarExcluirModelo(modelo)"
                      title="Excluir este modelo"
                    >
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- Sem agrupamento (modo tradicional) -->
          <template v-else>
            <div class="models-container w-100">
              <div v-for="modelo in safePatientModelos" :key="modelo.url" class="modelo-card"
                draggable="true"
                @dragstart="onModeloDragStart($event, modelo)"
              >
                <div class="modelo-preview" @click="openModelViewer(modelo)">
                  <font-awesome-icon :icon="['fas', 'cube']" size="3x" class="modelo-icon" />
                  <div class="modelo-info">
                    <span class="modelo-name">{{ modelo.descricao || 'Modelo 3D' }}</span>
                    <span class="modelo-date">{{ $filters.dateDmy(modelo.data) }}</span>
                  </div>
                  <button
                    class="modelo-delete-btn"
                    @click.stop="confirmarExcluirModelo(modelo)"
                    title="Excluir este modelo"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </template>
        </div>
        <Transition>
          <div class="row mt-3" v-show="pendingModeloPreviews.length === 0">
            <div class="col-12 text-center">
              <input
                id="modeloFileInput"
                type="file"
                accept=".stl"
                multiple
                @change="setModeloPreviews"
                hidden
              />
              <button
                class="btn bg-gradient-primary btn-add-models"
                @click="chooseModeloFile"
              >
                <i class="fas fa-upload me-2"></i>
                Enviar Modelos 3D
              </button>
            </div>
          </div>
        </Transition>
        <Transition>
          <div v-if="pendingModeloPreviews.length > 0" class="row mt-3">
            <!-- Opções de agrupamento -->
            <div class="col-12 mb-3">
              <div class="grupo-options-container">
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    id="criarGrupoCheck"
                    v-model="criarGrupo"
                  >
                  <label class="form-check-label" for="criarGrupoCheck">
                    <font-awesome-icon :icon="['fas', 'layer-group']" class="me-2" />
                    Criar grupo para estes modelos
                  </label>
                </div>
                <div v-if="criarGrupo" class="mt-2">
                  <input
                    type="text"
                    class="form-control grupo-nome-input"
                    v-model="nomeGrupo"
                    placeholder="Nome do grupo (ex: Ortodontia, Mandíbula + Maxila)"
                    maxlength="100"
                  />
                  <small class="text-muted">
                    <font-awesome-icon :icon="['fas', 'info-circle']" class="me-1" />
                    Os modelos serão agrupados e exibidos juntos com controles individuais
                  </small>
                </div>
              </div>
            </div>

            <!-- Previews dos modelos -->
            <div class="col-12 d-flex flex-wrap justify-content-center gap-3">
              <div v-for="(_, index) in pendingModeloPreviews" :key="index" class="upload-preview">
                <div class="modelo-preview-icon">
                  <font-awesome-icon :icon="['fas', 'cube']" size="4x" />
                </div>
                <div class="modelo-preview-name">{{ pendingModeloFiles[index].name }}</div>
                <input type="date" class="text-center" v-model="pendingModeloMetadata[index].date" />
                <input type="text" class="text-center" v-model="pendingModeloMetadata[index].description" placeholder="Descrição" />
              </div>
            </div>

            <!-- Botões de ação -->
            <div class="col-12 text-center mt-3">
              <button
                class="btn btn-sm btn-danger me-2"
                @click="cancelModeloUpload"
              >
                Cancelar
              </button>
              <button
                class="btn btn-sm btn-success"
                @click="confirmModeloUpload"
                :disabled="criarGrupo && !nomeGrupo.trim()"
              >
                Confirmar Upload
              </button>
            </div>
          </div>
        </Transition>
      </div>
    </div>

    <!-- Modal para visualização do modelo 3D -->
    <div class="modal modelo3d-modal" id="modelViewerModal" tabindex="-1" aria-labelledby="modelViewerModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modelViewerModalLabel">
              <template v-if="currentGrupo">
                <font-awesome-icon :icon="['fas', 'layer-group']" class="me-2" />
                {{ currentGrupo.nome }}
              </template>
              <template v-else>
                Visualizador 3D
              </template>
            </h5>
            <button type="button" class="btn-close" aria-label="Close"></button>
          </div>

          <!-- Controles de grupo (se for um grupo) -->
          <div v-if="currentGrupo" class="modal-group-controls">
            <div class="group-models-toggles">
              <span class="controls-label">Modelos:</span>
              <div class="model-toggles">
                <button
                  v-for="(modelo, index) in currentGrupo.modelos"
                  :key="modelo.id"
                  class="btn btn-sm model-toggle-btn"
                  :class="isModeloVisivelNoViewer(modelo.id) ? 'btn-primary' : 'btn-outline-secondary'"
                  @click="toggleModeloVisibilidadeNoViewer(modelo.id)"
                  :title="modelo.descricao || `Modelo ${index + 1}`"
                >
                  <font-awesome-icon :icon="['fas', 'cube']" class="me-1" />
                  {{ modelo.descricao || `Modelo ${index + 1}` }}
                  <font-awesome-icon
                    :icon="['fas', isModeloVisivelNoViewer(modelo.id) ? 'eye' : 'eye-slash']"
                    class="ms-1"
                  />
                </button>
              </div>
            </div>
          </div>

          <div class="modal-body p-0">
            <!-- Visualizador para modelo único -->
            <div v-if="currentModeloUrl && !currentGrupo" style="width: 100%; height: 600px;">
              <Modelos3DViewer
                :modelUrl="currentModeloUrl"
                :pacienteNome="paciente.nome"
                :modeloDescricao="currentModelo?.descricao || 'Modelo 3D'"
                :modeloData="currentModelo?.data || ''"
                @error="handleViewerError"
                @close="closeModelViewer"
              />
            </div>

            <!-- Visualizador para grupo de modelos -->
            <div v-else-if="currentGrupo" style="width: 100%; height: 600px;">
              <div class="grupo-viewer-container">
                <div
                  v-for="(modelo, index) in currentGrupo.modelos"
                  :key="modelo.id"
                  class="modelo-viewer-instance"
                  :class="{ 'modelo-hidden': !isModeloVisivelNoViewer(modelo.id) }"
                  :style="{ zIndex: isModeloVisivelNoViewer(modelo.id) ? 10 + index : 1 }"
                >
                  <Modelos3DViewer
                    :modelUrl="modelo.url"
                    :pacienteNome="paciente.nome"
                    :modeloDescricao="modelo.descricao || `Modelo ${index + 1}`"
                    :modeloData="modelo.data || ''"
                    :isGroupMode="true"
                    :modelIndex="index"
                    @error="handleViewerError"
                    @close="closeModelViewer"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Zona de exclusão para modelos arrastados -->
    <div
      class="modelo-delete-zone"
      :class="{ 'active': isDraggingModelo, 'highlight': isOverDeleteZone }"
      ref="deleteZone"
      @dragover.prevent="onDragOverDeleteZone"
      @dragleave.prevent="onDragLeaveDeleteZone"
      @drop.prevent="onDropOnDeleteZone"
    >
      <div class="delete-zone-content">
        <i class="fas fa-trash"></i>
        <span>Solte aqui para excluir</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.models-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  padding: 15px 10px;
  background: #f8f9fa;
  border-radius: 0 0 8px 8px;
  gap: 12px;
  min-height: 125px;
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
}

/* Estilo para o container de modelos quando não está agrupado por data */
template:not([v-else-if="groupByDate"]) .models-container {
  border-radius: 8px;
  margin-top: 10px;
  margin-bottom: 10px;
}

.modelo-card {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  width: 160px;
  height: 160px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.modelo-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 15px;
  text-align: center;
  position: relative;
}

.modelo-icon {
  color: #0d6efd;
  margin-bottom: 10px;
}

.modelo-info {
  display: flex;
  flex-direction: column;
}

.modelo-delete-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background-color: rgba(220, 53, 69, 0.85);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-size: 11px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
  z-index: 10;
  opacity: 0;
  transform: scale(0.8);
}

.modelo-preview:hover .modelo-delete-btn {
  opacity: 1;
  transform: scale(1);
}

.modelo-delete-btn:hover {
  background-color: #dc3545;
  transform: scale(1.1);
}

.modelo-name {
  font-weight: bold;
  font-size: 0.9rem;
  margin-bottom: 5px;
  word-break: break-word;
}

.modelo-desc, .modelo-date {
  font-size: 0.8rem;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.modelo-date {
  color: #0d6efd;
  font-size: 0.75rem;
  margin-top: 2px;
}

.modelo-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.modelo3d-group-container {
  background: transparent;
  min-height: 300px;
  max-height: calc(100svh - 700px);
  overflow-y: auto;
  transition: all 0.3s ease;
  position: relative;
  padding: 0;
  margin: 0 auto;
  max-width: 1400px;
  border: none;
  scrollbar-width: thin;
  scrollbar-color: #dee2e6 transparent;
}

.modelo3d-group-container::-webkit-scrollbar {
  width: 8px;
}

.modelo3d-group-container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

.modelo3d-group-container::-webkit-scrollbar-thumb {
  background-color: #dee2e6;
  border-radius: 10px;
  border: 2px solid transparent;
}

.modelo3d-group-container.drag-over {
  border: 2px dashed #0d6efd;
  background-color: rgba(13, 110, 253, 0.03);
  border-radius: 12px;
}

.date-group {
  margin-bottom: 20px;
}

.date-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 15px;
  background: #e9ecef;
  border-radius: 8px 8px 0 0;
  font-weight: 500;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  transition: all 0.2s ease;
}

.date-group-header-content {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
}

.date-text {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-weight: 500;
}

.date-group-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1;
}

.date-delete-all-btn {
  background-color: transparent;
  color: #dc3545;
  border: none;
  font-size: 0.85rem;
  padding: 4px 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.date-delete-all-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
  opacity: 1;
}

.date-how-much {
  font-weight: 600;
  color: #495057;
  font-size: 0.85rem;
  background-color: rgba(13, 110, 253, 0.1);
  padding: 4px 10px;
  border-radius: 4px;
  margin-right: 8px;
  transition: all 0.2s ease;
  border: 1px solid rgba(13, 110, 253, 0.15);
  z-index: 1;
}

.no-models-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6c757d;
}

.message-content {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.loading-models-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6c757d;
}

.loading-spinner {
  margin-bottom: 20px;
}

.loading-text {
  font-size: 1rem;
  font-weight: 500;
  color: #0d6efd;
}

.btn-add-models {
  margin-top: 10px;
}

.upload-preview {
  padding: 10px;
  border: 1px dashed #ccc;
  border-radius: 5px;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 180px;
}

.modelo-preview-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  color: #0d6efd;
}

.modelo-preview-name {
  font-size: 0.9rem;
  text-align: center;
  margin-bottom: 10px;
  word-break: break-word;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.upload-preview input[type="date"],
.upload-preview input[type="text"] {
  width: 100%;
  box-sizing: border-box;
  padding: 6px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

/* Estilos para opções de agrupamento */
.grupo-options-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.grupo-options-container .form-check-label {
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.grupo-options-container .form-check-input {
  margin-top: 0;
  transform: scale(1.1);
}

.grupo-nome-input {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  padding: 10px 15px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.grupo-nome-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  outline: none;
}

.grupo-options-container small {
  display: block;
  margin-top: 8px;
  font-size: 0.85rem;
}

/* Estilos para cards de grupo */
.grupo-card {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.grupo-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.grupo-preview {
  background: transparent !important;
  color: white;
}

.grupo-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  z-index: 10;
}

.grupo-icon-main {
  color: rgba(255, 255, 255, 0.9);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.grupo-name {
  color: white !important;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.grupo-info {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 0.85rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.grupo-models-preview {
  position: absolute;
  bottom: 8px;
  right: 8px;
  z-index: 10;
}

.mini-models {
  display: flex;
  gap: 4px;
  align-items: center;
}

.mini-model {
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.9);
}

.mini-model-more {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  margin-left: 4px;
}

.grupo-delete-btn {
  background: rgba(220, 53, 69, 0.8) !important;
  border: none !important;
  backdrop-filter: blur(5px);
}

.grupo-delete-btn:hover {
  background: rgba(220, 53, 69, 1) !important;
  transform: scale(1.1);
}



/* Estilos para seção de modelos avulsos */
.modelos-avulsos-container {
  margin-top: 20px;
}

.avulsos-header {
  background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
  padding: 12px 20px;
  border-radius: 8px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #5f6368;
  font-weight: 500;
}



.stl-viewer {
  width: 100%;
  height: 500px;
  background: #f0f0f0;
  transition: opacity 0.3s ease;
  touch-action: none; /* Importante para evitar problemas de toque em dispositivos móveis */
  outline: none; /* Remover contorno quando o elemento recebe foco */
  cursor: grab; /* Cursor indicando que o objeto pode ser arrastado */
}

.stl-viewer:active {
  cursor: grabbing; /* Cursor quando está arrastando */
}

.stl-viewer.hidden {
  opacity: 0;
}

.stl-loader-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(240, 240, 240, 0.8);
  z-index: 10;
}

.stl-loader-spinner {
  margin-bottom: 20px;
}

.stl-loader-progress {
  width: 80%;
  max-width: 400px;
  text-align: center;
}

.stl-loader-text {
  margin-top: 10px;
  font-weight: 500;
  color: #0d6efd;
}

.progress {
  height: 10px;
  border-radius: 5px;
  background-color: #e9ecef;
  margin-bottom: 5px;
}

.viewer-controls {
  position: absolute;
  bottom: 15px;
  left: 15px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.controls-group {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.horizontal-controls {
  display: flex;
  gap: 5px;
}

.control-btn {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #555;
}

.control-btn:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.control-btn:active {
  transform: translateY(0);
  background-color: #e8e8e8;
}

.reset-btn {
  background-color: #e8f4ff;
  color: #0d6efd;
}

.reset-btn:hover {
  background-color: #d1e7ff;
}

.zoom-controls, .view-controls {
  flex-direction: row;
  gap: 5px;
}

.viewer-instructions {
  position: absolute;
  bottom: 15px;
  right: 15px;
  z-index: 10;
  pointer-events: none;
}

.instructions-content {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 10px 15px;
  font-size: 0.8rem;
  color: #333;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  line-height: 1.5;
}

.modelo-delete-zone {
  position: fixed;
  bottom: -100px;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.9);
  border: 2px dashed #dc3545;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
}

.modelo-delete-zone.active {
  bottom: 20px;
  opacity: 1;
  pointer-events: auto;
}

.modelo-delete-zone.highlight {
  background-color: rgba(220, 53, 69, 0.1);
  border-color: #dc3545;
  transform: translateX(-50%) scale(1.05);
}

.delete-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #dc3545;
}

.delete-zone-content i {
  font-size: 24px;
  margin-bottom: 5px;
}

/* Estilos para o modal personalizado */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1055;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
  display: none;
  transition: opacity 0.15s linear;
  opacity: 0;
}

.modal.show {
  display: block;
  opacity: 1;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
  max-width: 800px;
  margin: 1.75rem auto;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - 3.5rem);
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  opacity: 0;
  transition: opacity 0.15s linear;
}

.modal-backdrop.show {
  opacity: 0.5;
}

.btn-close {
  box-sizing: content-box;
  width: 1em;
  height: 1em;
  padding: 0.25em;
  color: #000;
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
  border: 0;
  border-radius: 0.25rem;
  opacity: 0.5;
  cursor: pointer;
}

.btn-close:hover {
  opacity: 0.75;
}

.modal-xl {
  max-width: 1140px;
}

/* Estilos para o modal do visualizador 3D */
.modelo3d-modal .modal-dialog {
  max-width: 90%;
}

.modelo3d-modal .modal-content {
  border-radius: 12px;
  overflow: hidden;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modelo3d-modal .modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #eaeaea;
  padding: 12px 20px;
}

.modelo3d-modal .modal-title {
  font-weight: 600;
  color: #333;
}

.modelo3d-modal .btn-close {
  background-color: #e9ecef;
  border-radius: 50%;
  padding: 8px;
  opacity: 0.8;
  transition: all 0.2s ease;
}

.modelo3d-modal .btn-close:hover {
  opacity: 1;
  background-color: #dee2e6;
  transform: rotate(90deg);
}

/* Garantir que o modal fique acima do overlay de tela cheia */
.modal {
  z-index: 1200 !important;
}

.modal-backdrop {
  z-index: 1100 !important;
}

/* Estilo para o elemento fantasma durante o arrasto */
.drag-ghost {
  background-color: rgba(13, 110, 253, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Estilos para controles de grupo no modal */
.modal-group-controls {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  padding: 15px 20px;
}

.group-models-toggles {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.controls-label {
  font-weight: 600;
  color: #495057;
  font-size: 0.95rem;
  white-space: nowrap;
}

.model-toggles {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.model-toggle-btn {
  padding: 6px 12px;
  font-size: 0.85rem;
  border-radius: 20px;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 4px;
}

.model-toggle-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.model-toggle-btn.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
}

.model-toggle-btn.btn-outline-secondary {
  background: rgba(108, 117, 125, 0.1);
  border-color: rgba(108, 117, 125, 0.3);
  color: #6c757d;
}

.model-toggle-btn.btn-outline-secondary:hover {
  background: rgba(108, 117, 125, 0.2);
  border-color: rgba(108, 117, 125, 0.5);
  color: #495057;
}

/* Estilos para visualizador de grupo */
.grupo-viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.modelo-viewer-instance {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.modelo-viewer-instance.modelo-hidden {
  opacity: 0;
  pointer-events: none;
  transform: scale(0.95);
}

/* Responsividade para controles de grupo */
@media (max-width: 768px) {
  .modal-group-controls {
    padding: 12px 15px;
  }

  .group-models-toggles {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .model-toggles {
    width: 100%;
    justify-content: flex-start;
  }

  .model-toggle-btn {
    font-size: 0.8rem;
    padding: 5px 10px;
  }
}
</style>

<script>
import { uploadModelo3D, getModelos3D, getModelos3DAgrupados, excluirModelo3D } from "@/services/modelos3dService";
import cSwal from "@/utils/cSwal.js";
import Modelos3DViewer from "./Modelos3DViewer.vue";

export default {
  name: "Modelos3D",
  components: {
    Modelos3DViewer
  },
  props: {
    paciente: {
      type: Object,
      default: () => ({ modelos3d: [] }),
    },
    groupByDate: {
      type: Boolean,
      default: false
    },
    enableGrouping: {
      type: Boolean,
      default: true
    }
  },
  emits: ["pacienteChange"],
  setup() {
    return {};
  },
  data() {
    return {
      pendingModeloFiles: [],
      pendingModeloPreviews: [],
      pendingModeloMetadata: [],
      criarGrupo: false, // Checkbox para criar grupo
      nomeGrupo: '', // Nome do grupo
      dragOverContainer: false,
      isDraggingModelo: false,
      isOverDeleteZone: false,
      currentModeloViewer: null,
      currentModeloUrl: null,
      currentModelo: null, // Modelo atual sendo visualizado
      currentGrupo: null, // Grupo atual sendo visualizado
      grupoModelosVisiveis: {}, // Controla visibilidade dos modelos dentro do visualizador
      isLoadingModelo: false,
      loadingProgress: 0,
      modelos3d: [], // Array para armazenar os modelos 3D carregados
      isLoadingModelos: false, // Flag para indicar se os modelos estão sendo carregados
      grupos: [], // Array para armazenar grupos de modelos
      modelosAvulsos: [], // Array para modelos sem grupo
    };
  },
  computed: {
    safePatientModelos() {
      // Usar os modelos carregados do estado local em vez de acessar diretamente do paciente
      return this.modelos3d.length > 0 ? this.modelos3d : [];
    },
    groupedModelosByDate() {
      const groups = {};
      this.safePatientModelos.forEach((modelo) => {
        const date = modelo.data || "Sem data";
        if (!groups[date]) {
          groups[date] = [];
        }

        // O backend já formata as URLs dos modelos 3D corretamente
        // Apenas garantir que temos uma URL válida
        const modeloWithUrl = {
          ...modelo,
          url: modelo.url || ''
        };

        groups[date].push(modeloWithUrl);
      });
      // Convert to array of { date, modelos } sorted by date descending
      return Object.keys(groups)
        .map((date) => ({ date, modelos: groups[date] }))
        .sort((a, b) => (a.date < b.date ? 1 : -1));
    },
  },
  methods: {
    getModeloName(modelo) {
      // Extract filename from URL or use description
      if (modelo.url) {
        const urlParts = modelo.url.split('/');
        return urlParts[urlParts.length - 1];
      }
      return modelo.descricao || "Modelo 3D";
    },
    chooseModeloFile() {
      document.getElementById("modeloFileInput").click();
    },
    setModeloPreviews(event) {
      const files = Array.from(event.target.files).filter(file =>
        file.name.toLowerCase().endsWith('.stl')
      );

      if (files.length === 0) {
        cSwal.cError("Por favor, selecione arquivos STL válidos.");
        return;
      }

      this.pendingModeloFiles = files;
      this.pendingModeloPreviews = Array(files.length).fill("placeholder");
      this.pendingModeloMetadata = files.map(() => ({
        date: new Date().toISOString().slice(0, 10),
        description: "",
      }));
    },
    cancelModeloUpload() {
      this.pendingModeloFiles = [];
      this.pendingModeloPreviews = [];
      this.pendingModeloMetadata = [];
      this.criarGrupo = false;
      this.nomeGrupo = '';
      const fileInput = document.getElementById("modeloFileInput");
      if (fileInput) fileInput.value = "";
    },
    async confirmModeloUpload() {
      if (this.pendingModeloFiles.length === 0) {
        cSwal.cError("Nenhum arquivo selecionado.");
        return;
      }

      cSwal.loading("Adicionando modelos 3D...");

      try {
        // Determinar o nome do grupo se aplicável
        const grupoNome = this.criarGrupo && this.nomeGrupo.trim() ? this.nomeGrupo.trim() : null;

        for (let i = 0; i < this.pendingModeloFiles.length; i++) {
          const file = this.pendingModeloFiles[i];
          const modeloData = {
            paciente_id: this.paciente.id,
            modelo: file, // Será renomeado para 'modelo3d' no serviço
            dir: "modelo3d",
            data: this.pendingModeloMetadata[i]?.date || new Date().toISOString().slice(0, 10),
            descricao: this.pendingModeloMetadata[i]?.description || "",
            grupo_nome: grupoNome,
            tipo: "stl",
          };
          await uploadModelo3D(modeloData);
        }
        cSwal.loaded();
        cSwal.cSuccess("Modelos 3D adicionados com sucesso.");
        this.cancelModeloUpload();

        // Recarregar os modelos 3D após o upload
        await this.loadModelos3D();

        this.$emit("pacienteChange");
      } catch (error) {
        cSwal.loaded();
        console.error("Erro no upload de modelos 3D:", error);
        cSwal.cError("Erro inesperado ao adicionar os modelos 3D.");
      }
    },
    onDragOverContainer() {
      this.dragOverContainer = true;
    },
    onDragLeaveContainer() {
      this.dragOverContainer = false;
    },
    onDropContainer(event) {
      this.dragOverContainer = false;
      const dt = event.dataTransfer;
      if (!dt) return;

      const files = Array.from(dt.files).filter(file =>
        file.name.toLowerCase().endsWith('.stl')
      );

      if (files.length === 0) return;

      // Simulate input change event with these files
      const dataTransfer = new DataTransfer();
      files.forEach(file => dataTransfer.items.add(file));
      const fileInput = document.getElementById('modeloFileInput');
      if (fileInput) {
        fileInput.files = dataTransfer.files;
        // Trigger change event manually
        const event = new Event('change', { bubbles: true });
        fileInput.dispatchEvent(event);
      }
    },
    async openModelViewer(modelo) {
      // Initialize modal
      const modalElement = document.getElementById('modelViewerModal');

      // Set current model and URL
      this.currentModelo = modelo;
      this.currentModeloUrl = modelo.url;

      // Adicionar backdrop
      let backdrop = document.querySelector('.modal-backdrop');
      if (!backdrop) {
        backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop';
        document.body.appendChild(backdrop);

        // Forçar reflow para permitir a animação
        backdrop.offsetHeight;

        // Adicionar classe show para animar
        backdrop.classList.add('show');
      }

      // Preparar o modal
      modalElement.style.display = 'block';
      modalElement.setAttribute('aria-modal', 'true');
      modalElement.setAttribute('role', 'dialog');
      document.body.classList.add('modal-open');

      // Forçar reflow para permitir a animação
      modalElement.offsetHeight;

      // Adicionar classe show para animar
      modalElement.classList.add('show');

      // Configurar o botão de fechar
      const closeButton = modalElement.querySelector('.btn-close');
      if (closeButton) {
        // Remover qualquer listener anterior para evitar duplicação
        closeButton.removeEventListener('click', this.closeModelViewer);
        // Adicionar novo listener
        closeButton.addEventListener('click', this.closeModelViewer);
      }

      // Adicionar evento para fechar com ESC
      document.addEventListener('keydown', this.handleEscKey);
    },

    handleViewerError(error) {
      console.error('Erro no visualizador 3D:', error);
      cSwal.cError('Erro ao visualizar o modelo 3D. Por favor, tente novamente.');
      this.closeModelViewer();
    },

    closeModelViewer() {
      const modalElement = document.getElementById('modelViewerModal');

      // Esconder o modal com animação
      modalElement.classList.remove('show');

      // Remover backdrop com animação
      const backdrop = document.querySelector('.modal-backdrop');
      if (backdrop) {
        backdrop.classList.remove('show');
      }

      // Aguardar a animação terminar antes de remover completamente
      setTimeout(() => {
        modalElement.style.display = 'none';
        modalElement.removeAttribute('aria-modal');
        modalElement.removeAttribute('role');
        document.body.classList.remove('modal-open');

        // Remover backdrop do DOM
        if (backdrop && backdrop.parentNode) {
          backdrop.parentNode.removeChild(backdrop);
        }

        // Limpar a URL e o modelo atual para desmontar o componente visualizador
        this.currentModeloUrl = null;
        this.currentModelo = null;
        this.currentGrupo = null;
        this.grupoModelosVisiveis = {};
      }, 150); // 150ms é aproximadamente a duração da animação

      // Remover evento de ESC
      document.removeEventListener('keydown', this.handleEscKey);
    },

    handleEscKey(event) {
      if (event.key === 'Escape') {
        this.closeModelViewer();
      }
    },
    disposeSTLViewer() {
      // Método mantido para compatibilidade, mas não faz nada
      this.currentModeloViewer = null;
    },
    onDragOverDeleteZone() {
      this.isOverDeleteZone = true;
    },
    onDragLeaveDeleteZone() {
      this.isOverDeleteZone = false;
    },
    onDropOnDeleteZone(event) {
      this.isOverDeleteZone = false;
      this.isDraggingModelo = false;

      // Verificar se temos dados de modelo arrastado
      if (event.dataTransfer && event.dataTransfer.getData('modelo-id')) {
        const modeloId = event.dataTransfer.getData('modelo-id');
        if (modeloId) {
          this.confirmarExcluirModelo({ id: modeloId });
        }
      }
    },

    confirmarExcluirModelo(modelo) {
      if (!modelo || !modelo.id) {
        cSwal.cError("Não foi possível identificar o modelo para exclusão.");
        return;
      }

      // Usando o método cConfirm do cSwal com o formato correto
      cSwal.cConfirm(
        "Tem certeza que deseja excluir este modelo 3D?<br><br>Esta ação não pode ser desfeita.",
        () => this.excluirModelo(modelo.id),
        {
          title: "Excluir modelo 3D",
          icon: "warning",
          confirmButtonText: "Sim, excluir",
          cancelButtonText: "Cancelar",
        }
      );
    },

    async excluirModelo(id) {
      cSwal.loading("Excluindo modelo 3D...");

      try {
        const resultado = await excluirModelo3D(id);

        if (resultado) {
          cSwal.loaded();
          cSwal.cSuccess("Modelo 3D excluído com sucesso.");
          await this.loadModelos3D(); // Recarregar os modelos
          this.$emit("pacienteChange"); // Atualizar o paciente
        } else {
          cSwal.loaded();
          cSwal.cError("Não foi possível excluir o modelo 3D. Tente novamente.");
        }
      } catch (error) {
        console.error("Erro ao excluir modelo 3D:", error);
        cSwal.loaded();
        cSwal.cError("Ocorreu um erro ao excluir o modelo 3D.");
      }
    },

    confirmarExcluirTodosModelos(data) {
      if (!data) {
        cSwal.cError("Data inválida para exclusão de modelos.");
        return;
      }

      // Formatando a data para exibição
      const dataFormatada = this.$filters.dateLong(data);

      // Usando o método cConfirm do cSwal com o formato correto
      cSwal.cConfirm(
        `Tem certeza que deseja excluir todos os modelos 3D de <b>${dataFormatada}</b>?<br><br>Esta ação não pode ser desfeita.`,
        () => this.excluirTodosModelosPorData(data),
        {
          title: "Excluir todos os modelos",
          icon: "warning",
          confirmButtonText: "Sim, excluir todos",
          cancelButtonText: "Cancelar",
        }
      );
    },

    async excluirTodosModelosPorData(data) {
      if (!this.paciente || !this.paciente.id) {
        cSwal.cError("Paciente não identificado.");
        return;
      }

      cSwal.loading("Excluindo modelos 3D...");

      try {
        // Obter todos os modelos da data específica
        const modelosDaData = this.groupedModelosByDate.find(group => group.date === data)?.modelos || [];

        if (modelosDaData.length === 0) {
          cSwal.loaded();
          cSwal.cError("Nenhum modelo encontrado para esta data.");
          return;
        }

        // Excluir cada modelo individualmente
        for (const modelo of modelosDaData) {
          if (modelo.id) {
            await excluirModelo3D(modelo.id);
          }
        }

        cSwal.loaded();
        cSwal.cSuccess("Todos os modelos 3D foram excluídos com sucesso.");
        await this.loadModelos3D(); // Recarregar os modelos
        this.$emit("pacienteChange"); // Atualizar o paciente
      } catch (error) {
        console.error("Erro ao excluir modelos 3D por data:", error);
        cSwal.loaded();
        cSwal.cError("Ocorreu um erro ao excluir os modelos 3D.");
      }
    },

    /**
     * Carrega os modelos 3D do paciente
     */
    async loadModelos3D() {
      if (!this.paciente || !this.paciente.id) return;

      this.isLoadingModelos = true;

      try {
        if (this.enableGrouping) {
          // Usar o serviço para buscar os modelos 3D agrupados
          const dadosAgrupados = await getModelos3DAgrupados(this.paciente.id);

          // Atualizar o estado local com dados agrupados
          this.grupos = dadosAgrupados.grupos || [];
          this.modelosAvulsos = dadosAgrupados.modelos_avulsos || [];

          // Manter compatibilidade com visualização não agrupada
          this.modelos3d = [
            ...this.modelosAvulsos,
            ...this.grupos.flatMap(grupo => grupo.modelos)
          ];


        } else {
          // Usar o serviço tradicional para buscar os modelos 3D
          const modelos = await getModelos3D(this.paciente.id);

          // Atualizar o estado local
          this.modelos3d = modelos || [];
          this.grupos = [];
          this.modelosAvulsos = modelos || [];
        }

      } catch (error) {
        console.error('Erro ao carregar modelos 3D:', error);
        cSwal.cError('Erro ao carregar os modelos 3D. Por favor, tente novamente.');
      } finally {
        this.isLoadingModelos = false;
      }
    },



    /**
     * Abre o visualizador para um grupo de modelos
     */
    async openGrupoViewer(grupo) {
      // Initialize modal
      const modalElement = document.getElementById('modelViewerModal');

      // Set current group and initialize visibility states
      this.currentGrupo = grupo;
      this.currentModelo = null; // Clear single model
      this.currentModeloUrl = null; // Will be handled by the viewer component

      // Initialize visibility states for all models in the group
      const grupoVisiveis = {};
      grupo.modelos.forEach(modelo => {
        grupoVisiveis[modelo.id] = true; // All visible by default
      });
      this.grupoModelosVisiveis = grupoVisiveis;

      // Adicionar backdrop
      let backdrop = document.querySelector('.modal-backdrop');
      if (!backdrop) {
        backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop';
        document.body.appendChild(backdrop);

        // Forçar reflow para permitir a animação
        backdrop.offsetHeight;

        // Adicionar classe show para animar
        backdrop.classList.add('show');
      }

      // Preparar o modal
      modalElement.style.display = 'block';
      modalElement.setAttribute('aria-modal', 'true');
      modalElement.setAttribute('role', 'dialog');
      document.body.classList.add('modal-open');

      // Forçar reflow para permitir a animação
      modalElement.offsetHeight;

      // Adicionar classe show para animar
      modalElement.classList.add('show');

      // Configurar o botão de fechar
      const closeButton = modalElement.querySelector('.btn-close');
      if (closeButton) {
        // Remover qualquer listener anterior para evitar duplicação
        closeButton.removeEventListener('click', this.closeModelViewer);
        // Adicionar novo listener
        closeButton.addEventListener('click', this.closeModelViewer);
      }

      // Configurar listener para ESC
      document.addEventListener('keydown', this.handleEscKey);
    },

    /**
     * Alterna a visibilidade de um modelo dentro do visualizador de grupo
     */
    toggleModeloVisibilidadeNoViewer(modeloId) {
      this.grupoModelosVisiveis = {
        ...this.grupoModelosVisiveis,
        [modeloId]: !this.grupoModelosVisiveis[modeloId]
      };
    },

    /**
     * Verifica se um modelo está visível no visualizador de grupo
     */
    isModeloVisivelNoViewer(modeloId) {
      return this.grupoModelosVisiveis[modeloId] !== false;
    },

    /**
     * Drag start para grupos
     */
    onGrupoDragStart(event, grupo) {
      this.isDraggingModelo = true;

      // Create a custom drag image
      const dragImage = document.createElement('div');
      dragImage.className = 'drag-ghost';
      dragImage.innerHTML = `
        <i class="fas fa-layer-group"></i>
        <span>Grupo: ${grupo.nome}</span>
      `;
      document.body.appendChild(dragImage);

      // Set the drag image
      event.dataTransfer.setDragImage(dragImage, 0, 0);

      // Store grupo data
      event.dataTransfer.setData('application/json', JSON.stringify({
        type: 'grupo',
        grupo: grupo
      }));

      // Remove the drag image after a short delay
      setTimeout(() => {
        if (document.body.contains(dragImage)) {
          document.body.removeChild(dragImage);
        }
      }, 0);
    },

    /**
     * Confirma exclusão de um grupo inteiro
     */
    confirmarExcluirGrupo(grupo) {
      cSwal.cConfirm(
        `Deseja realmente excluir o grupo "${grupo.nome}" com todos os seus ${grupo.modelos.length} modelos?`,
        async () => {
          await this.excluirGrupo(grupo);
        }
      );
    },

    /**
     * Exclui todos os modelos de um grupo
     */
    async excluirGrupo(grupo) {
      cSwal.loading("Excluindo grupo...");

      try {
        // Excluir todos os modelos do grupo
        for (const modelo of grupo.modelos) {
          const resultado = await excluirModelo3D(modelo.id);
          if (!resultado) {
            throw new Error(`Falha ao excluir modelo ${modelo.descricao || modelo.id}`);
          }
        }

        cSwal.loaded();
        cSwal.cSuccess(`Grupo "${grupo.nome}" excluído com sucesso.`);
        await this.loadModelos3D(); // Recarregar os modelos
        this.$emit("pacienteChange"); // Atualizar o paciente
      } catch (error) {
        console.error("Erro ao excluir grupo:", error);
        cSwal.loaded();
        cSwal.cError("Ocorreu um erro ao excluir o grupo.");
      }
    },

    onModeloDragStart(event, modelo) {
      if (!modelo || !modelo.id) return;

      // Definir o cursor de arrasto
      event.dataTransfer.effectAllowed = 'move';

      // Armazenar o ID do modelo sendo arrastado
      event.dataTransfer.setData('modelo-id', modelo.id);

      // Ativar a zona de exclusão
      this.isDraggingModelo = true;

      // Adicionar uma imagem fantasma personalizada para o arrasto
      const ghostElement = document.createElement('div');
      ghostElement.classList.add('drag-ghost');
      ghostElement.innerHTML = `<i class="fas fa-cube"></i> ${modelo.descricao || 'Modelo 3D'}`;
      ghostElement.style.position = 'absolute';
      ghostElement.style.top = '-1000px';
      document.body.appendChild(ghostElement);
      event.dataTransfer.setDragImage(ghostElement, 20, 20);

      // Remover o elemento fantasma após um pequeno delay
      setTimeout(() => {
        document.body.removeChild(ghostElement);
      }, 100);
    },

    // Métodos de controle do visualizador foram removidos pois agora usamos o componente Modelos3DViewer
  },
  async mounted() {
    // Add global event listeners for drag operations
    window.addEventListener('dragstart', () => {
      this.isDraggingModelo = true;
    });

    window.addEventListener('dragend', () => {
      this.isDraggingModelo = false;
    });

    // Carregar modelos 3D quando o componente for montado
    await this.loadModelos3D();
  },
  beforeUnmount() {
    // Clean up event listeners
    window.removeEventListener('dragstart', () => {
      this.isDraggingModelo = true;
    });

    window.removeEventListener('dragend', () => {
      this.isDraggingModelo = false;
    });

    // Remover evento de ESC
    document.removeEventListener('keydown', this.handleEscKey);

    // Fechar modal se estiver aberto
    const modalElement = document.getElementById('modelViewerModal');
    if (modalElement && modalElement.classList.contains('show')) {
      this.closeModelViewer();
    }
  }
};
</script>
