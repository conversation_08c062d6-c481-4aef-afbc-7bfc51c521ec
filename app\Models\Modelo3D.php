<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Modelo3D extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'modelos3d';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'paciente_id',
        'dir',
        'filename',
        'url',
        'data',
        'descricao',
        'grupo_nome',
        'is_diagnostico',
        'tag_diagnostico'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
        ];
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*']);
    }

    /**
     * Scope para buscar modelos agrupados por nome do grupo
     */
    public function scopeComGrupo($query, $grupoNome)
    {
        return $query->where('grupo_nome', $grupoNome);
    }

    /**
     * Scope para buscar modelos sem grupo (avulsos)
     */
    public function scopeSemGrupo($query)
    {
        return $query->whereNull('grupo_nome');
    }

    /**
     * Scope para buscar modelos agrupados de um paciente
     */
    public function scopeAgrupadosPorPaciente($query, $pacienteId)
    {
        return $query->where('paciente_id', $pacienteId)
                    ->whereNotNull('grupo_nome')
                    ->orderBy('grupo_nome')
                    ->orderBy('created_at');
    }

    /**
     * Verifica se o modelo faz parte de um grupo
     */
    public function temGrupo()
    {
        return !is_null($this->grupo_nome);
    }

    /**
     * Retorna todos os modelos do mesmo grupo
     */
    public function modelosDoGrupo()
    {
        if (!$this->temGrupo()) {
            return collect([$this]);
        }

        return static::where('paciente_id', $this->paciente_id)
                    ->where('grupo_nome', $this->grupo_nome)
                    ->orderBy('created_at')
                    ->get();
    }

    // Add a relationship to the Paciente model
    public function paciente()
    {
        return $this->belongsTo(Paciente::class, 'paciente_id');
    }
}
